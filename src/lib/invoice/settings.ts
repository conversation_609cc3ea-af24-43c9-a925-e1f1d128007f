import { createClient } from '@/lib/supabase/server';
import type { Database } from '@/lib/supabase/types';

export type InvoiceSettings = Database['public']['Tables']['invoice_settings']['Row'];

export interface InvoiceSettingsGrouped {
  company: Record<string, string>;
  payment: Record<string, string>;
  notes: Record<string, string>;
  template: Record<string, string>;
}

/**
 * Fetch all invoice settings from the database
 */
export async function getInvoiceSettings(): Promise<InvoiceSettings[]> {
  const supabase = await createClient();
  
  const { data, error } = await supabase
    .from('invoice_settings')
    .select('*')
    .eq('is_active', true)
    .order('category', { ascending: true })
    .order('setting_key', { ascending: true });

  if (error) {
    console.error('Error fetching invoice settings:', error);
    throw new Error('Failed to fetch invoice settings');
  }

  return data || [];
}

/**
 * Fetch invoice settings grouped by category
 */
export async function getInvoiceSettingsGrouped(): Promise<InvoiceSettingsGrouped> {
  const settings = await getInvoiceSettings();
  
  const grouped: InvoiceSettingsGrouped = {
    company: {},
    payment: {},
    notes: {},
    template: {}
  };

  settings.forEach(setting => {
    if (setting.setting_value && setting.category in grouped) {
      const category = setting.category as keyof InvoiceSettingsGrouped;
      grouped[category][setting.setting_key] = setting.setting_value;
    }
  });

  return grouped;
}

/**
 * Get a specific setting value by key
 */
export async function getInvoiceSetting(key: string): Promise<string | null> {
  const supabase = await createClient();
  
  const { data, error } = await supabase
    .from('invoice_settings')
    .select('setting_value')
    .eq('setting_key', key)
    .eq('is_active', true)
    .maybeSingle();

  if (error) {
    console.error(`Error fetching setting ${key}:`, error);
    return null;
  }

  return data?.setting_value || null;
}

/**
 * Update a specific setting value
 */
export async function updateInvoiceSetting(key: string, value: string): Promise<boolean> {
  const supabase = await createClient();
  
  const { error } = await supabase
    .from('invoice_settings')
    .update({ setting_value: value })
    .eq('setting_key', key);

  if (error) {
    console.error(`Error updating setting ${key}:`, error);
    return false;
  }

  return true;
}

/**
 * Update multiple settings at once
 */
export async function updateInvoiceSettings(settings: Record<string, string>): Promise<boolean> {
  const supabase = await createClient();
  
  try {
    // Update each setting individually
    const updates = Object.entries(settings).map(([key, value]) =>
      supabase
        .from('invoice_settings')
        .update({ setting_value: value })
        .eq('setting_key', key)
    );

    const results = await Promise.all(updates);
    
    // Check if any updates failed
    const hasErrors = results.some(result => result.error);
    
    if (hasErrors) {
      console.error('Some settings failed to update');
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error updating settings:', error);
    return false;
  }
}

/**
 * Get default invoice settings for fallback
 */
export function getDefaultInvoiceSettings(): InvoiceSettingsGrouped {
  return {
    company: {
      company_name: 'PT Harun Studio Indonesia',
      company_logo_url: 'https://via.placeholder.com/200x100?text=Harun+Studio',
      company_email: '<EMAIL>'
    },
    payment: {
      payment_bank_name: 'Bank Central Asia (BCA)',
      payment_account_number: '**********',
      payment_account_name: 'PT Harun Studio Indonesia',
      payment_method: 'Transfer Bank',
      payment_instruction: 'Harap sertakan nomor invoice pada deskripsi pembayaran'
    },
    notes: {
      default_thank_you_message: 'Terima kasih atas kepercayaan Anda menggunakan jasa kami.',
      payment_terms: 'Pembayaran dilakukan dalam waktu 14 hari sejak invoice diterima.',
      contact_message: 'Jika ada pertanyaan mengenai invoice ini, silakan hubungi <NAME_EMAIL>'
    },
    template: {
      pdf_footer_message: 'Thank you for your business!',
      pdf_company_tagline: 'Harun Studio'
    }
  };
}
