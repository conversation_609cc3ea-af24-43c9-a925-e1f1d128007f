import { createClient } from './client'
import { useEffect, useState } from 'react'
import { User, Session } from '@supabase/supabase-js'
import { Database } from './types'

type SupabaseClientType = ReturnType<typeof createClient>

export function useSupabase() {
  const [supabase] = useState<SupabaseClientType>(() => createClient())
  const [user, setUser] = useState<User | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    async function getSession() {
      setLoading(true)
      const { data: { session } } = await supabase.auth.getSession()
      setSession(session)
      setUser(session?.user ?? null)
      setLoading(false)

      const { data: { subscription } } = supabase.auth.onAuthStateChange(
        (_, session) => {
          setSession(session)
          setUser(session?.user ?? null)
        }
      )

      return () => {
        subscription.unsubscribe()
      }
    }

    getSession()
  }, [supabase])

  return {
    supabase,
    user,
    session,
    loading,
  }
}

export type Tables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Row']
export type InsertTables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Insert']
export type UpdateTables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Update']
