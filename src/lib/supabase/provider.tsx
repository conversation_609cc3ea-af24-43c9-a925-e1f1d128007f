'use client'

import { createContext, useContext, useState, useEffect } from 'react'
import { createClient } from './client'
import { User, Session } from '@supabase/supabase-js'

type SupabaseContext = {
  supabase: ReturnType<typeof createClient>
  user: User | null
  session: Session | null
  loading: boolean
}

// Create a context for Supabase
const Context = createContext<SupabaseContext | undefined>(undefined)

export function SupabaseProvider({ children }: { children: React.ReactNode }) {
  const [supabase] = useState(() => createClient())
  const [user, setUser] = useState<User | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    async function getSession() {
      setLoading(true)
      const { data: { session } } = await supabase.auth.getSession()
      setSession(session)
      setUser(session?.user ?? null)
      setLoading(false)

      const { data: { subscription } } = supabase.auth.onAuthStateChange(
        (_, session) => {
          setSession(session)
          setUser(session?.user ?? null)
        }
      )

      return () => {
        subscription.unsubscribe()
      }
    }

    getSession()
  }, [supabase])

  const value = {
    supabase,
    user,
    session,
    loading,
  }

  return <Context.Provider value={value}>{children}</Context.Provider>
}

// Create a hook to use the Supabase context
export function useSupabase() {
  const context = useContext(Context)
  if (context === undefined) {
    throw new Error('useSupabase must be used within a SupabaseProvider')
  }
  return context
}
