import type { InvoiceData, InvoiceItem } from '@/app/(dashboard)/dashboard/types';
import { formatCurrency, formatDate } from './formatters';
import { getInvoiceSettingsGrouped, getDefaultInvoiceSettings } from '@/lib/invoice/settings';
import type { InvoiceSettingsGrouped } from '@/lib/invoice/settings';

/**
 * Generate invoice HTML template with dynamic settings
 */
export async function generateInvoiceHTML(invoice: InvoiceData, items: InvoiceItem[], settings?: InvoiceSettingsGrouped): Promise<string> {
  // Get settings from database or use provided settings or fallback to defaults
  let invoiceSettings: InvoiceSettingsGrouped;

  if (settings) {
    invoiceSettings = settings;
  } else {
    try {
      invoiceSettings = await getInvoiceSettingsGrouped();
    } catch (error) {
      console.warn('Failed to fetch invoice settings, using defaults:', error);
      invoiceSettings = getDefaultInvoiceSettings();
    }
  }
  // Proper type handling untuk client dan project
  const client = invoice.clients as { name?: string; company?: string; email?: string; phone?: string } || {};
  const project = invoice.projects as { name?: string; description?: string } || {};

  // Use dynamic logo URL from settings
  const logoUrl = invoiceSettings.company.company_logo_url || 'https://via.placeholder.com/200x100?text=Harun+Studio';
  
  // Calculate totals
  const subtotal = items.reduce((sum, item) => {
    // Ensure amount is treated as a number
    const amount = typeof item.amount === 'string' ? parseFloat(item.amount) : (item.amount || 0);
    return sum + amount;
  }, 0);
  
  const taxAmount = typeof invoice.tax_amount === 'string' 
    ? parseFloat(invoice.tax_amount || '0') 
    : (invoice.tax_amount || 0);
    
  const discountAmount = typeof invoice.discount_amount === 'string'
    ? parseFloat(invoice.discount_amount || '0')
    : (invoice.discount_amount || 0);
    
  const total = typeof invoice.total === 'string'
    ? parseFloat(invoice.total || '0')
    : (invoice.total || (subtotal + taxAmount - discountAmount));

  // Styles for the invoice template
  const styles = `
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
    
    body {
      font-family: 'Inter', sans-serif;
      color: #374151;
      line-height: 1.5;
      padding: 0;
      margin: 0;
    }
    
    .invoice-container {
      max-width: 800px;
      margin: 0 auto;
      padding: 40px 20px;
    }
    
    .invoice-header {
      display: flex;
      justify-content: space-between;
      margin-bottom: 40px;
    }
    
    .invoice-logo img {
      max-height: 80px;
    }
    
    .invoice-title {
      font-size: 32px;
      font-weight: 700;
      color: #111827;
    }
    
    .invoice-details {
      display: flex;
      justify-content: space-between;
      margin-bottom: 40px;
    }
    
    .invoice-details-left, .invoice-details-right {
      flex: 1;
    }
    
    .invoice-details-right {
      text-align: right;
    }
    
    .invoice-id {
      font-size: 18px;
      font-weight: 600;
      color: #111827;
      margin-bottom: 8px;
    }
    
    .status-badge {
      display: inline-block;
      padding: 6px 12px;
      border-radius: 20px;
      font-size: 14px;
      font-weight: 500;
      text-transform: uppercase;
    }
    
    .status-paid {
      background-color: #D1FAE5;
      color: #065F46;
    }
    
    .status-unpaid {
      background-color: #FEE2E2;
      color: #B91C1C;
    }
    
    .status-draft {
      background-color: #FEF3C7;
      color: #92400E;
    }
    
    .client-details, .dates-details {
      margin-top: 20px;
    }
    
    h3 {
      font-size: 16px;
      font-weight: 600;
      color: #111827;
      margin-bottom: 8px;
    }
    
    table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 40px;
      margin-bottom: 40px;
    }
    
    th {
      background-color: #F3F4F6;
      padding: 12px 16px;
      text-align: left;
      font-size: 14px;
      font-weight: 600;
      text-transform: uppercase;
      color: #6B7280;
    }
    
    th:last-child, td:last-child {
      text-align: right;
    }
    
    td {
      padding: 16px;
      border-bottom: 1px solid #E5E7EB;
    }
    
    .item-description {
      color: #111827;
      font-weight: 500;
    }
    
    .item-details {
      color: #6B7280;
      font-size: 14px;
    }
    
    .totals {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      margin-top: 20px;
    }
    
    .totals-row {
      display: flex;
      justify-content: space-between;
      width: 300px;
      padding: 8px 0;
      font-size: 16px;
    }
    
    .totals-row.total {
      border-top: 2px solid #E5E7EB;
      margin-top: 8px;
      padding-top: 16px;
      font-weight: 700;
      color: #111827;
      font-size: 18px;
    }
    
    .notes {
      margin-top: 40px;
      padding: 20px;
      background-color: #F3F4F6;
      border-radius: 8px;
    }
    
    .footer {
      margin-top: 60px;
      text-align: center;
      font-size: 14px;
      color: #6B7280;
    }
  `;

  // Invoice content template
  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Invoice ${invoice.invoice_number}</title>
      <style>${styles}</style>
    </head>
    <body>
      <div class="invoice-container">
        <div class="invoice-header">
          <div class="invoice-logo">
            <img src="${logoUrl}" alt="Company Logo">
          </div>
          <div>
            <div class="invoice-title">INVOICE</div>
            <div class="invoice-id">#${invoice.invoice_number}</div>
            <div class="status-badge status-${(invoice.status || 'draft').toLowerCase()}">${invoice.status || 'Draft'}</div>
          </div>
        </div>
        
        <div class="invoice-details">
          <div class="invoice-details-left">
            <div class="client-details">
              <h3>Billed To</h3>
              <div>${client?.name || 'N/A'}</div>
              ${client?.company ? `<div>${client.company}</div>` : ''}
              ${client?.email ? `<div>${client.email}</div>` : ''}
              ${client?.phone ? `<div>${client.phone}</div>` : ''}
            </div>
          </div>
          
          <div class="invoice-details-right">
            <div class="dates-details">
              <h3>Invoice Details</h3>
              <div><strong>Issue Date:</strong> ${formatDate(invoice.issue_date)}</div>
              <div><strong>Due Date:</strong> ${formatDate(invoice.due_date)}</div>
              ${project?.name ? `<div><strong>Project:</strong> ${project.name}</div>` : ''}
            </div>
          </div>
        </div>
        
        <table>
          <thead>
            <tr>
              <th>Description</th>
              <th>Quantity</th>
              <th>Unit Price</th>
              <th>Amount</th>
            </tr>
          </thead>
          <tbody>
            ${items.map(item => `
              <tr>
                <td>
                  <div class="item-description">${item.description}</div>
                </td>
                <td>${item.quantity}</td>
                <td>${formatCurrency(item.unit_price, invoice.currency || 'USD')}</td>
                <td>${formatCurrency(item.amount, invoice.currency || 'USD')}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>
        
        <div class="totals">
          <div class="totals-row">
            <div>Subtotal</div>
            <div>${formatCurrency(subtotal, invoice.currency || 'USD')}</div>
          </div>
          
          ${discountAmount > 0 ? `
            <div class="totals-row">
              <div>Discount</div>
              <div>-${formatCurrency(discountAmount, invoice.currency || 'USD')}</div>
            </div>
          ` : ''}
          
          ${taxAmount > 0 ? `
            <div class="totals-row">
              <div>Tax${invoice.tax_rate ? ` (${invoice.tax_rate}%)` : ''}</div>
              <div>${formatCurrency(taxAmount, invoice.currency || 'USD')}</div>
            </div>
          ` : ''}
          
          <div class="totals-row total">
            <div>Total</div>
            <div>${formatCurrency(total, invoice.currency || 'USD')}</div>
          </div>
        </div>
        
        ${invoice.notes ? `
          <div class="notes">
            <h3>Notes</h3>
            <div>${invoice.notes}</div>
          </div>
        ` : ''}
        
        <div class="footer">
          <p>${invoiceSettings.template.pdf_footer_message || 'Thank you for your business!'}</p>
          <p>${invoiceSettings.template.pdf_company_tagline || 'Harun Studio'} • Generated on ${new Date().toLocaleDateString()}</p>
        </div>
      </div>
    </body>
    </html>
  `;
}
