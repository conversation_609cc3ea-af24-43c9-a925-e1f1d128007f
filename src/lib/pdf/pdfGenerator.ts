import puppeteer from 'puppeteer';

/**
 * Service untuk generate PDF menggunakan Puppeteer
 */
export class PdfGenerator {
  /**
   * Generate PDF dari HTML content
   */
  static async generateFromHtml(html: string): Promise<Buffer> {
    // Buka browser dalam mode headless
    const browser = await puppeteer.launch({
      headless: true, // 'new' tidak valid, gunakan boolean
    });
    
    try {
      const page = await browser.newPage();
      
      // Set HTML content
      await page.setContent(html);
      
      // Tunggu font terload
      await page.evaluateHandle('document.fonts.ready');
      
      // Tunggu hingga semua gambar terload
      await page.evaluate(async () => {
        const selectors = Array.from(document.querySelectorAll("img"));
        await Promise.all(selectors.map(img => {
          if (img.complete) return;
          return new Promise(resolve => {
            img.addEventListener('load', resolve);
            img.addEventListener('error', resolve);
          });
        }));
      });
      
      // Generate PDF
      const pdf = await page.pdf({
        format: 'A4',
        printBackground: true,
        margin: {
          top: '20px',
          right: '20px',
          bottom: '20px',
          left: '20px'
        }
      });
      
      return pdf;
    } finally {
      // Pastikan browser selalu ditutup
      await browser.close();
    }
  }
}
