/**
 * Utility functions untuk formatting data pada PDF invoice
 */

/**
 * Format tanggal dengan format yang lebih user-friendly
 */
export function formatDate(dateString: string | null | undefined): string {
  if (!dateString) return 'Not set';
  
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
}

/**
 * Format nilai sebagai mata uang
 */
export function formatCurrency(value: number | string | null | undefined, currency: string = 'USD'): string {
  if (value === null || value === undefined) return '-';
  
  // Convert string to number if needed
  const numericValue = typeof value === 'string' ? parseFloat(value) : value;
  
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency
  }).format(numericValue);
}

/**
 * Helper untuk mendapatkan warna status invoice
 */
export function getStatusColorClass(status: string | null | undefined): string {
  if (!status) return 'status-draft';
  
  switch (status.toLowerCase()) {
    case 'paid': return 'status-paid';
    case 'unpaid': return 'status-unpaid';
    case 'overdue': return 'status-overdue';
    case 'draft':
    default:
      return 'status-draft';
  }
}
