import { NextResponse } from 'next/server';
import { generateInvoiceHTML } from '@/lib/pdf/invoiceTemplate';
import { PdfGenerator } from '@/lib/pdf/pdfGenerator';
import { Database } from '@/lib/supabase/types';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { SupabaseClient } from '@supabase/supabase-js';

/**
 * API route untuk menghasilkan PDF invoice
 */
export async function POST(req: Request) {
  try {
    // Parse request body untuk mendapatkan invoiceId
    const { invoiceId } = await req.json();
    
    if (!invoiceId) {
      return NextResponse.json(
        { error: 'Invoice ID is required' },
        { status: 400 }
      );
    }
    
    // Mendukung dua metode autentikasi: cookies dan Authorization header
    // Perbaikan: Await cookies() sesuai Next.js 15 requirements dan gunakan SSR package
    const cookieStore = await cookies();
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return cookieStore.getAll()
          },
          setAll(cookiesToSet) {
            try {
              cookiesToSet.forEach(({ name, value, options }) =>
                cookieStore.set(name, value, options)
              )
            } catch {
              // The `setAll` method was called from a Server Component.
              // This can be ignored if you have middleware refreshing
              // user sessions.
            }
          },
        },
      }
    );
    
    // Coba cek session dari cookie terlebih dahulu
    const { data: { session } } = await supabase.auth.getSession();
    
    // Jika tidak ada session dari cookie, cek Authorization header
    if (!session) {
      const authHeader = req.headers.get('Authorization');
      if (authHeader?.startsWith('Bearer ')) {
        const token = authHeader.substring(7); // Remove 'Bearer ' prefix
        try {
          // Verifikasi token dan dapatkan session
          const { data, error } = await supabase.auth.getUser(token);
          if (error || !data.user) {
            throw new Error('Invalid token');
          }
          // Session valid dari token
        } catch (authError) {
          console.error('Auth error:', authError);
          return NextResponse.json(
            { error: 'Invalid authentication token' },
            { status: 401 }
          );
        }
      } else {
        // Tidak ada session dan tidak ada token yang valid
        return NextResponse.json(
          { error: 'Authentication required' },
          { status: 401 }
        );
      }
    }
    
    // Ambil data invoice dan items
    const invoiceData = await fetchInvoiceData(supabase, invoiceId);
    if ('error' in invoiceData) {
      return NextResponse.json(
        { error: invoiceData.error },
        { status: invoiceData.status }
      );
    }
    
    const { invoice, invoiceItems } = invoiceData;
    
    // Generate HTML template
    const html = generateInvoiceHTML(invoice, invoiceItems);
    
    // Generate PDF menggunakan puppeteer service
    const pdf = await PdfGenerator.generateFromHtml(html);
    
    // Return PDF sebagai response dengan header yang sesuai
    return new NextResponse(pdf, {
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="invoice-${invoice.invoice_number}.pdf"`,
      },
    });
  } catch (error) {
    console.error('Error generating PDF:', error);
    return NextResponse.json(
      { error: 'Failed to generate PDF' },
      { status: 500 }
    );
  }
}

/**
 * Mengambil data invoice dan invoice items dari Supabase
 */
async function fetchInvoiceData(supabase: SupabaseClient<Database>, invoiceId: string) {
  try {
    // Ambil data invoice dengan relasi - tambahkan error handling khusus untuk .single()
    const { data: invoice, error: invoiceError } = await supabase
      .from('invoices')
      .select(`
        *,
        clients (id, name, company, email, phone),
        projects (id, name, description)
      `)
      .eq('id', invoiceId)
      .maybeSingle(); // Gunakan maybeSingle() alih-alih single() untuk menghindari error ketika tidak ada data
    
    if (invoiceError || !invoice) {
      return {
        error: invoiceError?.message || 'Invoice not found',
        status: 404
      };
    }
    
    // Ambil invoice items
    const { data: invoiceItems, error: itemsError } = await supabase
      .from('invoice_items')
      .select('*')
      .eq('invoice_id', invoiceId)
      .order('created_at', { ascending: true });
    
    if (itemsError) {
      return {
        error: itemsError.message,
        status: 500
      };
    }
    
    // Peringatan jika tidak ada invoice items
    if (!invoiceItems || invoiceItems.length === 0) {
      console.warn('No invoice items found for invoice:', invoiceId);
    }
    
    return { invoice, invoiceItems };
  } catch (error) {
    console.error('Error fetching invoice data:', error);
    return {
      error: error instanceof Error ? error.message : 'Unknown error fetching invoice data',
      status: 500
    };
  }
}
