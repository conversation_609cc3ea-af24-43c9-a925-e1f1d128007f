'use client'

import { useEffect, useState } from 'react'
import { useSupabase } from '@/lib/supabase/provider'
import { LucideLoader } from 'lucide-react'
import ClientForm from '@/components/forms/ClientForm'
import Link from 'next/link'
import type { ClientData } from '@/app/(dashboard)/dashboard/types'

interface EditClientPageProps {
  params: {
    id: string
  }
}

export default function EditClientPage({ params }: EditClientPageProps) {
  const { supabase } = useSupabase()
  const [client, setClient] = useState<ClientData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchClient = async () => {
      try {
        setLoading(true)
        
        const { data, error } = await supabase
          .from('clients')
          .select('*')
          .eq('id', params.id)
          .single()
        
        if (error) throw error
        setClient(data)
      } catch (error) {
        console.error('Error fetching client:', error)
        setError(error instanceof Error ? error.message : 'Failed to load client data')
      } finally {
        setLoading(false)
      }
    }

    fetchClient()
  }, [params.id, supabase])

  if (loading) {
    return (
      <div className="p-4 md:p-6 flex items-center justify-center h-64">
        <LucideLoader className="h-8 w-8 animate-spin text-indigo-600" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-4 md:p-6">
        <div className="bg-red-50 p-4 rounded-md mb-4">
          <p className="text-red-700">{error}</p>
        </div>
        <Link
          href="/clients"
          className="text-indigo-600 hover:text-indigo-800"
        >
          Back to Clients
        </Link>
      </div>
    )
  }

  if (!client) {
    return (
      <div className="p-4 md:p-6">
        <div className="bg-yellow-50 p-4 rounded-md mb-4">
          <p className="text-yellow-700">Client not found</p>
        </div>
        <Link
          href="/clients"
          className="text-indigo-600 hover:text-indigo-800"
        >
          Back to Clients
        </Link>
      </div>
    )
  }

  return (
    <div className="p-4 md:p-6">
      <h1 className="text-2xl font-bold mb-6">Edit Client</h1>
      <div className="bg-white shadow rounded-lg p-6">
        <ClientForm clientId={params.id} defaultValues={client} />
      </div>
    </div>
  )
}
