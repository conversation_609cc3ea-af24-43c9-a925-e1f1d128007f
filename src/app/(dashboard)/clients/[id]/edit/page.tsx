'use client'

import { useEffect, useState } from 'react'
import { useParams } from 'next/navigation'
import { useSupabase } from '@/lib/supabase/provider'
import { LucideLoader, LucideChevronLeft } from 'lucide-react'
import ClientForm from '@/components/forms/ClientForm'
import Link from 'next/link'
import type { ClientData } from '@/app/(dashboard)/dashboard/types'
import { PageLayout, PageHeader, PageContent, Card, LoadingState } from '@/components/ui/page-layout'

export default function EditClientPage() {
  const params = useParams<{ id: string }>()
  const clientId = params.id
  const { supabase } = useSupabase()
  const [client, setClient] = useState<ClientData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchClient = async () => {
      if (!clientId) return

      try {
        setLoading(true)

        const { data, error } = await supabase
          .from('clients')
          .select('*')
          .eq('id', clientId)
          .single()

        if (error) throw error
        setClient(data)
      } catch (error) {
        console.error('Error fetching client:', error)
        setError(error instanceof Error ? error.message : 'Failed to load client data')
      } finally {
        setLoading(false)
      }
    }

    fetchClient()
  }, [clientId, supabase])

  if (loading) {
    return (
      <PageLayout>
        <PageContent>
          <LoadingState message="Loading client data..." />
        </PageContent>
      </PageLayout>
    )
  }

  if (error) {
    return (
      <PageLayout>
        <PageContent>
          <div className="bg-destructive/15 p-4 rounded-md mb-4">
            <p className="text-destructive">{error}</p>
          </div>
          <Link
            href="/clients"
            className="text-primary hover:text-primary/80"
          >
            Back to Clients
          </Link>
        </PageContent>
      </PageLayout>
    )
  }

  if (!client) {
    return (
      <PageLayout>
        <PageContent>
          <div className="bg-muted p-4 rounded-md mb-4">
            <p className="text-muted-foreground">Client not found</p>
          </div>
          <Link
            href="/clients"
            className="text-primary hover:text-primary/80"
          >
            Back to Clients
          </Link>
        </PageContent>
      </PageLayout>
    )
  }

  return (
    <PageLayout>
      <PageHeader
        title="Edit Client"
        description={client.name}
        breadcrumb={
          <Link
            href={`/clients/${clientId}`}
            className="inline-flex items-center text-sm text-muted-foreground hover:text-foreground"
          >
            <LucideChevronLeft className="mr-1 h-4 w-4" />
            Back to Client
          </Link>
        }
      />

      <PageContent>
        <Card>
          <ClientForm clientId={clientId} defaultValues={client} />
        </Card>
      </PageContent>
    </PageLayout>
  )
}
