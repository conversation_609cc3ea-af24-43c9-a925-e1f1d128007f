'use client'

import { useEffect, useState } from 'react'
import { useSupabase } from '@/lib/supabase/provider'
import { LucideLoader, LucideChevronLeft } from 'lucide-react'
import ClientForm from '@/components/forms/ClientForm'
import Link from 'next/link'
import type { ClientData } from '@/app/(dashboard)/dashboard/types'
import { PageLayout, PageHeader, PageContent, Card, LoadingState } from '@/components/ui/page-layout'

interface EditClientPageProps {
  params: {
    id: string
  }
}

export default function EditClientPage({ params }: EditClientPageProps) {
  const { supabase } = useSupabase()
  const [client, setClient] = useState<ClientData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchClient = async () => {
      try {
        setLoading(true)
        
        const { data, error } = await supabase
          .from('clients')
          .select('*')
          .eq('id', params.id)
          .single()
        
        if (error) throw error
        setClient(data)
      } catch (error) {
        console.error('Error fetching client:', error)
        setError(error instanceof Error ? error.message : 'Failed to load client data')
      } finally {
        setLoading(false)
      }
    }

    fetchClient()
  }, [params.id, supabase])

  if (loading) {
    return (
      <PageLayout>
        <PageContent>
          <LoadingState message="Loading client data..." />
        </PageContent>
      </PageLayout>
    )
  }

  if (error) {
    return (
      <PageLayout>
        <PageContent>
          <div className="bg-destructive/15 p-4 rounded-md mb-4">
            <p className="text-destructive">{error}</p>
          </div>
          <Link
            href="/clients"
            className="text-primary hover:text-primary/80"
          >
            Back to Clients
          </Link>
        </PageContent>
      </PageLayout>
    )
  }

  if (!client) {
    return (
      <PageLayout>
        <PageContent>
          <div className="bg-yellow-50 p-4 rounded-md mb-4">
            <p className="text-yellow-700">Client not found</p>
          </div>
          <Link
            href="/clients"
            className="text-primary hover:text-primary/80"
          >
            Back to Clients
          </Link>
        </PageContent>
      </PageLayout>
    )
  }

  return (
    <PageLayout>
      <PageHeader
        title="Edit Client"
        description={client.name}
        breadcrumb={
          <Link
            href={`/clients/${params.id}`}
            className="inline-flex items-center text-sm text-muted-foreground hover:text-foreground"
          >
            <LucideChevronLeft className="mr-1 h-4 w-4" />
            Back to Client
          </Link>
        }
      />

      <PageContent>
        <Card>
          <ClientForm clientId={params.id} defaultValues={client} />
        </Card>
      </PageContent>
    </PageLayout>
  )
}
