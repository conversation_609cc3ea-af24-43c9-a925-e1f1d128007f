'use client'

import { useState, useEffect, useCallback } from 'react'
import Link from 'next/link'
import { useRouter, useParams } from 'next/navigation'
import { useSupabase } from '@/lib/supabase/provider'
import { LucidePencil, LucideTrash2, LucideChevronLeft, LucideLoader } from 'lucide-react'
import type { ClientData, ProjectData, InvoiceData } from '@/app/(dashboard)/dashboard/types'
import { PageLayout, PageHeader, PageContent, PageSection, Card, TableWrapper, LoadingState, EmptyState } from '@/components/ui/page-layout'
import { Button } from '@/components/ui/button'

export default function ClientDetailPage() {
  const router = useRouter()
  const params = useParams<{ id: string }>()
  const clientId = params.id
  const { supabase } = useSupabase()
  const [client, setClient] = useState<ClientData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [projects, setProjects] = useState<ProjectData[]>([])
  const [invoices, setInvoices] = useState<InvoiceData[]>([])

  const fetchClientData = useCallback(async () => {
    if (!clientId) return

    try {
      setLoading(true)

      // Fetch client details
      const { data: clientData, error: clientError } = await supabase
        .from('clients')
        .select('*')
        .eq('id', clientId)
        .single()

      if (clientError) throw clientError
      setClient(clientData)

      // Fetch related projects
      const { data: projectsData, error: projectsError } = await supabase
        .from('projects')
        .select('id, name, client_id, status, value, currency, start_date, end_date')
        .eq('client_id', clientId)
        .order('created_at', { ascending: false })

      if (projectsError) throw projectsError
      setProjects(projectsData as ProjectData[] || [])

      // Fetch related invoices
      const { data: invoicesData, error: invoicesError } = await supabase
        .from('invoices')
        .select('id, client_id, invoice_number, status, total, currency, due_date')
        .eq('client_id', clientId)
        .order('created_at', { ascending: false })

      if (invoicesError) throw invoicesError
      setInvoices(invoicesData as InvoiceData[] || [])

    } catch (error) {
      console.error('Error fetching client data:', error)
      setError(error instanceof Error ? error.message : 'Failed to load client data')
    } finally {
      setLoading(false)
    }
  }, [clientId, supabase])

  useEffect(() => {
    fetchClientData()
  }, [fetchClientData])



  const handleDeleteClient = async () => {
    if (window.confirm('Are you sure you want to delete this client? This action cannot be undone.')) {
      try {
        setLoading(true)
        
        // Check for related projects and invoices
        if (projects.length > 0 || invoices.length > 0) {
          alert(
            'This client has related projects or invoices. Please remove or reassign them before deleting the client.'
          )
          setLoading(false)
          return
        }
        
        const { error } = await supabase
          .from('clients')
          .delete()
          .eq('id', clientId)
        
        if (error) throw error
        
        router.push('/clients')
        router.refresh()
      } catch (error) {
        console.error('Error deleting client:', error)
        setError(error instanceof Error ? error.message : 'Failed to delete client')
        setLoading(false)
      }
    }
  }

  if (loading) {
    return (
      <PageLayout>
        <PageContent>
          <LoadingState message="Loading client data..." />
        </PageContent>
      </PageLayout>
    )
  }

  if (error) {
    return (
      <PageLayout>
        <PageContent>
          <div className="bg-destructive/15 p-4 rounded-md mb-4">
            <p className="text-destructive">{error}</p>
          </div>
          <Link
            href="/clients"
            className="inline-flex items-center text-primary hover:text-primary/80"
          >
            <LucideChevronLeft className="h-4 w-4 mr-1" />
            Back to Clients
          </Link>
        </PageContent>
      </PageLayout>
    )
  }

  if (!client) {
    return (
      <PageLayout>
        <PageContent>
          <div className="bg-yellow-50 p-4 rounded-md mb-4">
            <p className="text-yellow-700">Client not found</p>
          </div>
          <Link
            href="/clients"
            className="inline-flex items-center text-primary hover:text-primary/80"
          >
            <LucideChevronLeft className="h-4 w-4 mr-1" />
            Back to Clients
          </Link>
        </PageContent>
      </PageLayout>
    )
  }

  return (
    <PageLayout>
      <PageHeader
        title={client.name}
        description="Client details and related information"
        breadcrumb={
          <Link
            href="/clients"
            className="inline-flex items-center text-sm text-muted-foreground hover:text-foreground"
          >
            <LucideChevronLeft className="h-4 w-4 mr-1" />
            Back to Clients
          </Link>
        }
        action={
          <div className="flex space-x-2">
            <Button variant="outline" asChild>
              <Link href={`/clients/${clientId}/edit`}>
                <LucidePencil className="h-4 w-4 mr-1" />
                Edit
              </Link>
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteClient}
              disabled={projects.length > 0 || invoices.length > 0}
              title={projects.length > 0 || invoices.length > 0 ? 'Cannot delete: has related projects or invoices' : 'Delete client'}
            >
              <LucideTrash2 className="h-4 w-4 mr-1" />
              Delete
            </Button>
          </div>
        }
      />

      <PageContent>

        {/* Client details */}
        <Card className="mb-6">
          <h2 className="text-lg font-medium text-card-foreground mb-4">Client Details</h2>
          <dl className="grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-6">
            <div>
              <dt className="text-sm font-medium text-muted-foreground">Company</dt>
              <dd className="mt-1 text-sm text-card-foreground">{client.company || '-'}</dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-muted-foreground">Status</dt>
              <dd className="mt-1">
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  client.status === 'active'
                    ? 'bg-green-100 text-green-800'
                    : client.status === 'inactive'
                    ? 'bg-gray-100 text-gray-800'
                    : 'bg-yellow-100 text-yellow-800'
                }`}>
                  {client.status || 'Unknown'}
                </span>
              </dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-muted-foreground">Email</dt>
              <dd className="mt-1 text-sm text-card-foreground">
                {client.email ? (
                  <a href={`mailto:${client.email}`} className="text-primary hover:text-primary/80">
                    {client.email}
                  </a>
                ) : (
                  '-'
                )}
              </dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-muted-foreground">Phone</dt>
              <dd className="mt-1 text-sm text-card-foreground">
                {client.phone ? (
                  <a href={`tel:${client.phone}`} className="text-primary hover:text-primary/80">
                    {client.phone}
                  </a>
                ) : (
                  '-'
                )}
              </dd>
            </div>
          </dl>
        </Card>

        {/* Projects section */}
        <PageSection title="Projects" className="mb-6">
          <TableWrapper>
            {projects.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-border">
                  <thead className="bg-muted/50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                        Name
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                        Status
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                        Value
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                        Timeline
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                        <span className="sr-only">Actions</span>
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-card divide-y divide-border">
                    {projects.map((project) => (
                      <tr key={project.id} className="hover:bg-accent">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-card-foreground">{project.name}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            project.status === 'completed'
                              ? 'bg-green-100 text-green-800'
                              : project.status === 'in-progress'
                              ? 'bg-blue-100 text-blue-800'
                              : project.status === 'on-hold'
                              ? 'bg-yellow-100 text-yellow-800'
                              : 'bg-gray-100 text-gray-800'
                          }`}>
                            {project.status || 'Not set'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-card-foreground">
                            {project.value ? `${project.currency || '$'}${project.value.toLocaleString()}` : '-'}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-muted-foreground">
                            {project.start_date && project.end_date
                              ? `${new Date(project.start_date).toLocaleDateString()} - ${new Date(project.end_date).toLocaleDateString()}`
                              : project.start_date
                              ? `From ${new Date(project.start_date).toLocaleDateString()}`
                              : '-'
                            }
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <Link
                            href={`/projects/${project.id}`}
                            className="text-primary hover:text-primary/80"
                          >
                            View
                          </Link>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <EmptyState
                title="No projects yet"
                description="No projects found for this client."
                action={
                  <Link
                    href={`/projects/new?client_id=${clientId}`}
                    className="inline-flex items-center text-primary hover:text-primary/80"
                  >
                    Create a new project
                  </Link>
                }
              />
            )}
          </TableWrapper>
        </PageSection>

        {/* Invoices section */}
        <PageSection title="Invoices">
          <TableWrapper>
            {invoices.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-border">
                  <thead className="bg-muted/50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                        Invoice #
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                        Status
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                        Total
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                        Due Date
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                        <span className="sr-only">Actions</span>
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-card divide-y divide-border">
                    {invoices.map((invoice) => (
                      <tr key={invoice.id} className="hover:bg-accent">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-card-foreground">{invoice.invoice_number}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            invoice.status === 'paid'
                              ? 'bg-green-100 text-green-800'
                              : invoice.status === 'overdue'
                              ? 'bg-red-100 text-red-800'
                              : invoice.status === 'sent'
                              ? 'bg-blue-100 text-blue-800'
                              : invoice.status === 'draft'
                              ? 'bg-gray-100 text-gray-800'
                              : 'bg-yellow-100 text-yellow-800'
                          }`}>
                            {invoice.status || 'Unknown'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-card-foreground">
                            {`${invoice.currency || '$'}${invoice.total.toLocaleString()}`}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-muted-foreground">
                            {invoice.due_date ? new Date(invoice.due_date).toLocaleDateString() : '-'}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <Link
                            href={`/invoices/${invoice.id}`}
                            className="text-primary hover:text-primary/80"
                          >
                            View
                          </Link>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <EmptyState
                title="No invoices yet"
                description="No invoices found for this client."
                action={
                  <Link
                    href={`/invoices/new?client_id=${clientId}`}
                    className="inline-flex items-center text-primary hover:text-primary/80"
                  >
                    Create a new invoice
                  </Link>
                }
              />
            )}
          </TableWrapper>
        </PageSection>
      </PageContent>
    </PageLayout>
  )
}
