'use client'

import { useState, useEffect, useCallback } from 'react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useSupabase } from '@/lib/supabase/provider'
import { LucidePencil, LucideTrash2, LucideChevronLeft, LucideLoader } from 'lucide-react'
import type { ClientData, ProjectData, InvoiceData } from '@/app/(dashboard)/dashboard/types'

interface ClientDetailPageProps {
  params: {
    id: string
  }
}

export default function ClientDetailPage({ params }: ClientDetailPageProps) {
  const router = useRouter()
  const { supabase } = useSupabase()
  const [client, setClient] = useState<ClientData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [projects, setProjects] = useState<ProjectData[]>([])
  const [invoices, setInvoices] = useState<InvoiceData[]>([])

  const fetchClientData = useCallback(async () => {
    try {
      setLoading(true)
      
      // Fetch client details
      const { data: clientData, error: clientError } = await supabase
        .from('clients')
        .select('*')
        .eq('id', params.id)
        .single()
      
      if (clientError) throw clientError
      setClient(clientData)
      
      // Fetch related projects
      const { data: projectsData, error: projectsError } = await supabase
        .from('projects')
        .select('id, name, client_id, status, value, currency, start_date, end_date')
        .eq('client_id', params.id)
        .order('created_at', { ascending: false })
      
      if (projectsError) throw projectsError
      setProjects(projectsData as ProjectData[] || [])
      
      // Fetch related invoices
      const { data: invoicesData, error: invoicesError } = await supabase
        .from('invoices')
        .select('id, client_id, invoice_number, status, total, currency, due_date')
        .eq('client_id', params.id)
        .order('created_at', { ascending: false })
      
      if (invoicesError) throw invoicesError
      setInvoices(invoicesData as InvoiceData[] || [])
      
    } catch (error) {
      console.error('Error fetching client data:', error)
      setError(error instanceof Error ? error.message : 'Failed to load client data')
    } finally {
      setLoading(false)
    }
  }, [params.id, supabase])

  useEffect(() => {
    fetchClientData()
  }, [fetchClientData])



  const handleDeleteClient = async () => {
    if (window.confirm('Are you sure you want to delete this client? This action cannot be undone.')) {
      try {
        setLoading(true)
        
        // Check for related projects and invoices
        if (projects.length > 0 || invoices.length > 0) {
          alert(
            'This client has related projects or invoices. Please remove or reassign them before deleting the client.'
          )
          setLoading(false)
          return
        }
        
        const { error } = await supabase
          .from('clients')
          .delete()
          .eq('id', params.id)
        
        if (error) throw error
        
        router.push('/clients')
        router.refresh()
      } catch (error) {
        console.error('Error deleting client:', error)
        setError(error instanceof Error ? error.message : 'Failed to delete client')
        setLoading(false)
      }
    }
  }

  if (loading) {
    return (
      <div className="p-4 md:p-6 flex items-center justify-center h-64">
        <LucideLoader className="h-8 w-8 animate-spin text-indigo-600" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-4 md:p-6">
        <div className="bg-red-50 p-4 rounded-md mb-4">
          <p className="text-red-700">{error}</p>
        </div>
        <Link
          href="/clients"
          className="inline-flex items-center text-indigo-600 hover:text-indigo-800"
        >
          <LucideChevronLeft className="h-4 w-4 mr-1" />
          Back to Clients
        </Link>
      </div>
    )
  }

  if (!client) {
    return (
      <div className="p-4 md:p-6">
        <div className="bg-yellow-50 p-4 rounded-md mb-4">
          <p className="text-yellow-700">Client not found</p>
        </div>
        <Link
          href="/clients"
          className="inline-flex items-center text-indigo-600 hover:text-indigo-800"
        >
          <LucideChevronLeft className="h-4 w-4 mr-1" />
          Back to Clients
        </Link>
      </div>
    )
  }

  return (
    <div className="p-4 md:p-6">
      {/* Header with actions */}
      <div className="mb-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-3">
          <h1 className="text-2xl font-bold">{client.name}</h1>
          <div className="mt-2 sm:mt-0 flex space-x-2">
            <Link
              href={`/clients/${params.id}/edit`}
              className="inline-flex items-center rounded-md bg-indigo-50 px-3 py-2 text-sm font-medium text-indigo-700 hover:bg-indigo-100"
            >
              <LucidePencil className="h-4 w-4 mr-1" />
              Edit
            </Link>
            <button
              onClick={handleDeleteClient}
              className="inline-flex items-center rounded-md bg-red-50 px-3 py-2 text-sm font-medium text-red-700 hover:bg-red-100"
              disabled={projects.length > 0 || invoices.length > 0}
              title={projects.length > 0 || invoices.length > 0 ? 'Cannot delete: has related projects or invoices' : 'Delete client'}
            >
              <LucideTrash2 className="h-4 w-4 mr-1" />
              Delete
            </button>
          </div>
        </div>
        <Link
          href="/clients"
          className="inline-flex items-center text-sm text-indigo-600 hover:text-indigo-800"
        >
          <LucideChevronLeft className="h-4 w-4 mr-1" />
          Back to Clients
        </Link>
      </div>

      {/* Client details */}
      <div className="bg-white shadow rounded-lg overflow-hidden mb-6">
        <div className="p-6">
          <dl className="grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-6">
            <div>
              <dt className="text-sm font-medium text-gray-500">Company</dt>
              <dd className="mt-1 text-sm text-gray-900">{client.company || '-'}</dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">Status</dt>
              <dd className="mt-1">
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  client.status === 'active' 
                    ? 'bg-green-100 text-green-800' 
                    : client.status === 'inactive' 
                    ? 'bg-gray-100 text-gray-800' 
                    : 'bg-yellow-100 text-yellow-800'
                }`}>
                  {client.status || 'Unknown'}
                </span>
              </dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">Email</dt>
              <dd className="mt-1 text-sm text-gray-900">
                {client.email ? (
                  <a href={`mailto:${client.email}`} className="text-indigo-600 hover:text-indigo-800">
                    {client.email}
                  </a>
                ) : (
                  '-'
                )}
              </dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">Phone</dt>
              <dd className="mt-1 text-sm text-gray-900">
                {client.phone ? (
                  <a href={`tel:${client.phone}`} className="text-indigo-600 hover:text-indigo-800">
                    {client.phone}
                  </a>
                ) : (
                  '-'
                )}
              </dd>
            </div>
          </dl>
        </div>
      </div>

      {/* Projects section */}
      <h2 className="text-xl font-semibold mb-4">Projects</h2>
      <div className="bg-white shadow rounded-lg overflow-hidden mb-6">
        {projects.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Name
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Value
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Timeline
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <span className="sr-only">Actions</span>
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {projects.map((project) => (
                  <tr key={project.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{project.name}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        project.status === 'completed' 
                          ? 'bg-green-100 text-green-800' 
                          : project.status === 'in-progress' 
                          ? 'bg-blue-100 text-blue-800'
                          : project.status === 'on-hold'
                          ? 'bg-yellow-100 text-yellow-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {project.status || 'Not set'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {project.value ? `${project.currency || '$'}${project.value.toLocaleString()}` : '-'}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-500">
                        {project.start_date && project.end_date
                          ? `${new Date(project.start_date).toLocaleDateString()} - ${new Date(project.end_date).toLocaleDateString()}`
                          : project.start_date
                          ? `From ${new Date(project.start_date).toLocaleDateString()}`
                          : '-'
                        }
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <Link 
                        href={`/projects/${project.id}`}
                        className="text-indigo-600 hover:text-indigo-900"
                      >
                        View
                      </Link>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="p-6 text-center text-sm text-gray-500">
            No projects found for this client.
            <div className="mt-2">
              <Link
                href={`/projects/new?client_id=${params.id}`}
                className="inline-flex items-center text-indigo-600 hover:text-indigo-800"
              >
                Create a new project
              </Link>
            </div>
          </div>
        )}
      </div>

      {/* Invoices section */}
      <h2 className="text-xl font-semibold mb-4">Invoices</h2>
      <div className="bg-white shadow rounded-lg overflow-hidden">
        {invoices.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Invoice #
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Total
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Due Date
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <span className="sr-only">Actions</span>
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {invoices.map((invoice) => (
                  <tr key={invoice.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{invoice.invoice_number}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        invoice.status === 'paid' 
                          ? 'bg-green-100 text-green-800' 
                          : invoice.status === 'overdue' 
                          ? 'bg-red-100 text-red-800'
                          : invoice.status === 'sent'
                          ? 'bg-blue-100 text-blue-800'
                          : invoice.status === 'draft'
                          ? 'bg-gray-100 text-gray-800'
                          : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {invoice.status || 'Unknown'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {`${invoice.currency || '$'}${invoice.total.toLocaleString()}`}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-500">
                        {invoice.due_date ? new Date(invoice.due_date).toLocaleDateString() : '-'}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <Link 
                        href={`/invoices/${invoice.id}`}
                        className="text-indigo-600 hover:text-indigo-900"
                      >
                        View
                      </Link>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="p-6 text-center text-sm text-gray-500">
            No invoices found for this client.
            <div className="mt-2">
              <Link
                href={`/invoices/new?client_id=${params.id}`}
                className="inline-flex items-center text-indigo-600 hover:text-indigo-800"
              >
                Create a new invoice
              </Link>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
