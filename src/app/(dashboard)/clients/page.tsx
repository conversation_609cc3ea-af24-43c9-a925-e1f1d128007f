'use client'

import { useState, useEffect, useCallback } from 'react'
import Link from 'next/link'
import { useSupabase } from '@/lib/supabase/provider'
import { PageLayout, PageHeader, PageContent, TableWrapper, LoadingState, EmptyState } from '@/components/ui/page-layout'
import { Button } from '@/components/ui/button'
import { LucidePlus, LucideSearch, LucidePencil, LucideTrash2, LucideMoreHorizontal } from 'lucide-react'
import type { ClientData } from '../dashboard/types'

export default function ClientsPage() {
  const { supabase } = useSupabase()
  const [clients, setClients] = useState<ClientData[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const clientsPerPage = 10

  const fetchClients = useCallback(async () => {
    try {
      setLoading(true)
      
      let query = supabase
        .from('clients')
        .select('id, name, company, email, phone, status')
        .order('name', { ascending: true })
      
      if (searchTerm) {
        query = query.or(`name.ilike.%${searchTerm}%,company.ilike.%${searchTerm}%,email.ilike.%${searchTerm}%`)
      }
      
      const { data, error } = await query
      
      if (error) {
        console.error('Error fetching clients:', error)
        return
      }
      
      setClients(data)
    } catch (error) {
      console.error('Error:', error)
    } finally {
      setLoading(false)
    }
  }, [searchTerm, supabase])

  useEffect(() => {
    fetchClients()
  }, [fetchClients])



  // Filter clients based on search term
  const filteredClients = clients.filter(client => 
    client.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    client.company?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    client.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    client.phone?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  // Calculate pagination
  const indexOfLastClient = currentPage * clientsPerPage
  const indexOfFirstClient = indexOfLastClient - clientsPerPage
  const currentClients = filteredClients.slice(indexOfFirstClient, indexOfLastClient)
  const totalPages = Math.ceil(filteredClients.length / clientsPerPage)

  const handleDeleteClient = async (clientId: string) => {
    if (window.confirm('Are you sure you want to delete this client?')) {
      try {
        const { error } = await supabase.from('clients').delete().eq('id', clientId)
        
        if (error) {
          console.error('Error deleting client:', error)
          return
        }
        
        // Refresh the client list
        fetchClients()
      } catch (error) {
        console.error('Error:', error)
      }
    }
  }

  return (
    <PageLayout>
      <PageHeader
        title="Clients"
        description="Manage your client relationships and contact information"
        action={
          <div className="flex flex-col sm:flex-row w-full md:w-auto gap-3">
            <div className="relative flex-grow">
              <input
                type="text"
                placeholder="Search clients..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-input rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent"
              />
              <LucideSearch
                className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground"
                aria-hidden="true"
              />
            </div>
            <Button asChild>
              <Link href="/clients/new">
                <LucidePlus className="mr-2 h-4 w-4" />
                New Client
              </Link>
            </Button>
          </div>
        }
      />

      <PageContent>

        <TableWrapper>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-border">
              <thead className="bg-muted/50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                    Name
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                    Company
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                    Email
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                    Phone
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                    Status
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-muted-foreground uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-card divide-y divide-border">
                {loading ? (
                  <tr>
                    <td colSpan={6} className="px-6 py-4 text-center">
                      <LoadingState message="Loading clients..." />
                    </td>
                  </tr>
                ) : currentClients.length > 0 ? (
                  currentClients.map((client) => (
                    <tr key={client.id} className="hover:bg-accent">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-card-foreground">{client.name}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-muted-foreground">{client.company || '-'}</div>
                    </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-muted-foreground">{client.email || '-'}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-muted-foreground">{client.phone || '-'}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          client.status === 'active'
                            ? 'bg-green-100 text-green-800'
                            : client.status === 'inactive'
                            ? 'bg-muted text-muted-foreground'
                            : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {client.status || 'Unknown'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex justify-end space-x-2">
                          <Link
                            href={`/clients/${client.id}`}
                            className="text-primary hover:text-primary/80 p-1"
                          >
                          <LucideMoreHorizontal className="h-5 w-5" />
                        </Link>
                          <Link
                            href={`/clients/${client.id}/edit`}
                            className="text-primary hover:text-primary/80 p-1"
                          >
                            <LucidePencil className="h-5 w-5" />
                          </Link>
                          <button
                            onClick={() => handleDeleteClient(client.id)}
                            className="text-destructive hover:text-destructive/80 p-1"
                          >
                            <LucideTrash2 className="h-5 w-5" />
                          </button>
                      </div>
                    </td>
                  </tr>
                ))
                ) : (
                  <tr>
                    <td colSpan={6} className="px-6 py-4 text-center">
                      <EmptyState
                        title={searchTerm ? 'No clients match your search' : 'No clients found'}
                        description={searchTerm ? 'Try adjusting your search terms' : 'Create your first client to get started'}
                      />
                    </td>
                  </tr>
                )}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {filteredClients.length > clientsPerPage && (
          <div className="flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6">
            <div className="flex flex-1 justify-between sm:hidden">
              <button
                onClick={() => setCurrentPage(currentPage > 1 ? currentPage - 1 : 1)}
                disabled={currentPage === 1}
                className={`relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 ${
                  currentPage === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-50'
                }`}
              >
                Previous
              </button>
              <button
                onClick={() => setCurrentPage(currentPage < totalPages ? currentPage + 1 : totalPages)}
                disabled={currentPage === totalPages}
                className={`relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 ${
                  currentPage === totalPages ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-50'
                }`}
              >
                Next
              </button>
            </div>
            <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-gray-700">
                  Showing <span className="font-medium">{indexOfFirstClient + 1}</span> to{' '}
                  <span className="font-medium">
                    {Math.min(indexOfLastClient, filteredClients.length)}
                  </span>{' '}
                  of <span className="font-medium">{filteredClients.length}</span> clients
                </p>
              </div>
              <div>
                <nav className="isolate inline-flex -space-x-px rounded-md border border-border" aria-label="Pagination">
                  {Array.from({ length: totalPages }).map((_, idx) => (
                    <Button
                      key={idx}
                      variant={currentPage === idx + 1 ? "default" : "outline"}
                      size="sm"
                      onClick={() => setCurrentPage(idx + 1)}
                      className="relative inline-flex items-center px-4 py-2 text-sm font-semibold rounded-none first:rounded-l-md last:rounded-r-md"
                    >
                      {idx + 1}
                    </Button>
                  ))}
                </nav>
              </div>
            </div>
          </div>
        )}
        </TableWrapper>
      </PageContent>
    </PageLayout>
  )
}
