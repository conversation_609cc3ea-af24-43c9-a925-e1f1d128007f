'use client'

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, PageContent } from '@/components/ui/page-layout'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, <PERSON><PERSON>Content } from '@/components/ui/tabs'
import InvoiceSettings from './components/InvoiceSettings'

export default function SettingsPage() {
  return (
    <PageLayout>
      <PageHeader 
        title="Settings" 
        description="Configure your application preferences and business settings"
      />

      <PageContent>
        <Tabs defaultValue="invoice" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="invoice">Invoice</TabsTrigger>
            <TabsTrigger value="general">General</TabsTrigger>
            <TabsTrigger value="users">Users</TabsTrigger>
            <TabsTrigger value="integrations">Integrations</TabsTrigger>
          </TabsList>
          
          <TabsContent value="invoice" className="space-y-6">
            <InvoiceSettings />
          </TabsContent>
          
          <TabsContent value="general" className="space-y-6">
            <div className="text-center py-12">
              <h3 className="text-lg font-medium text-foreground mb-2">General Settings</h3>
              <p className="text-muted-foreground">General application settings will be available here.</p>
            </div>
          </TabsContent>
          
          <TabsContent value="users" className="space-y-6">
            <div className="text-center py-12">
              <h3 className="text-lg font-medium text-foreground mb-2">User Management</h3>
              <p className="text-muted-foreground">User management and permissions will be available here.</p>
            </div>
          </TabsContent>
          
          <TabsContent value="integrations" className="space-y-6">
            <div className="text-center py-12">
              <h3 className="text-lg font-medium text-foreground mb-2">Integrations</h3>
              <p className="text-muted-foreground">Third-party integrations and API settings will be available here.</p>
            </div>
          </TabsContent>
        </Tabs>
      </PageContent>
    </PageLayout>
  )
}
