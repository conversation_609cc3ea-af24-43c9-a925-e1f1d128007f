'use client'

import { useState, useEffect } from 'react'
import { useSupabase } from '@/lib/supabase/provider'
import type { InvoiceSettings, InvoiceSettingsGrouped } from '@/lib/invoice/settings'

export default function InvoiceSettingsPage() {
  const { supabase } = useSupabase()
  const [settings, setSettings] = useState<InvoiceSettings[]>([])
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null)

  useEffect(() => {
    fetchSettings()
  }, [])

  async function fetchSettings() {
    try {
      setLoading(true)
      const { data, error } = await supabase
        .from('invoice_settings')
        .select('*')
        .eq('is_active', true)
        .order('category', { ascending: true })
        .order('setting_key', { ascending: true })

      if (error) throw error
      setSettings(data || [])
    } catch (error) {
      console.error('Error fetching settings:', error)
      setMessage({ type: 'error', text: 'Failed to load settings' })
    } finally {
      setLoading(false)
    }
  }

  async function handleSave(settingKey: string, value: string) {
    try {
      setSaving(true)
      const { error } = await supabase
        .from('invoice_settings')
        .update({ setting_value: value })
        .eq('setting_key', settingKey)

      if (error) throw error

      // Update local state
      setSettings(prev => 
        prev.map(setting => 
          setting.setting_key === settingKey 
            ? { ...setting, setting_value: value }
            : setting
        )
      )

      setMessage({ type: 'success', text: 'Setting updated successfully' })
      setTimeout(() => setMessage(null), 3000)
    } catch (error) {
      console.error('Error updating setting:', error)
      setMessage({ type: 'error', text: 'Failed to update setting' })
    } finally {
      setSaving(false)
    }
  }

  function handleInputChange(settingKey: string, value: string) {
    setSettings(prev => 
      prev.map(setting => 
        setting.setting_key === settingKey 
          ? { ...setting, setting_value: value }
          : setting
      )
    )
  }

  const groupedSettings = settings.reduce((acc, setting) => {
    if (!acc[setting.category]) {
      acc[setting.category] = []
    }
    acc[setting.category].push(setting)
    return acc
  }, {} as Record<string, InvoiceSettings[]>)

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="space-y-4">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-20 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-foreground">Invoice Settings</h1>
        <p className="text-muted-foreground mt-2">
          Configure invoice template settings including payment information, company details, and default messages.
        </p>
      </div>

      {message && (
        <div className={`mb-6 p-4 rounded-lg border ${
          message.type === 'success'
            ? 'bg-green-50 text-green-800 border-green-200'
            : 'bg-destructive/10 text-destructive border-destructive/20'
        }`}>
          {message.text}
        </div>
      )}

      <div className="space-y-8">
        {Object.entries(groupedSettings).map(([category, categorySettings]) => (
          <div key={category} className="bg-card rounded-lg border border-border p-6">
            <h2 className="text-lg font-semibold text-card-foreground mb-4 capitalize">
              {category === 'company' && 'Company Information'}
              {category === 'payment' && 'Payment Information'}
              {category === 'notes' && 'Invoice Notes & Terms'}
              {category === 'template' && 'PDF Template Settings'}
            </h2>
            
            <div className="grid grid-cols-1 gap-6">
              {categorySettings.map(setting => (
                <div key={setting.id} className="space-y-2">
                  <label className="block text-sm font-medium text-foreground">
                    {setting.display_name}
                  </label>
                  {setting.description && (
                    <p className="text-xs text-muted-foreground">{setting.description}</p>
                  )}
                  
                  {setting.setting_type === 'url' || setting.setting_type === 'email' ? (
                    <input
                      type={setting.setting_type}
                      value={setting.setting_value || ''}
                      onChange={(e) => handleInputChange(setting.setting_key, e.target.value)}
                      onBlur={(e) => handleSave(setting.setting_key, e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      disabled={saving}
                    />
                  ) : setting.setting_key.includes('message') || setting.setting_key.includes('terms') || setting.setting_key.includes('instruction') ? (
                    <textarea
                      value={setting.setting_value || ''}
                      onChange={(e) => handleInputChange(setting.setting_key, e.target.value)}
                      onBlur={(e) => handleSave(setting.setting_key, e.target.value)}
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      disabled={saving}
                    />
                  ) : (
                    <input
                      type="text"
                      value={setting.setting_value || ''}
                      onChange={(e) => handleInputChange(setting.setting_key, e.target.value)}
                      onBlur={(e) => handleSave(setting.setting_key, e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      disabled={saving}
                    />
                  )}
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>

      <div className="mt-8 p-4 bg-blue-50 rounded-lg border border-blue-200">
        <h3 className="text-sm font-medium text-blue-900 mb-2">How to use these settings:</h3>
        <ul className="text-sm text-blue-800 space-y-1">
          <li>• Changes are automatically saved when you finish editing a field</li>
          <li>• These settings will be used in all new invoices and PDF generation</li>
          <li>• Company information appears in invoice headers and footers</li>
          <li>• Payment information is displayed in invoice payment sections</li>
          <li>• Notes and terms are shown at the bottom of invoices</li>
        </ul>
      </div>
    </div>
  )
}
