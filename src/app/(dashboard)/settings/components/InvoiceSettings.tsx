'use client'

import { useState, useEffect } from 'react'
import { useSupabase } from '@/lib/supabase/provider'
import { Card } from '@/components/ui/page-layout'
import { Button } from '@/components/ui/button'
import type { InvoiceSettings, InvoiceSettingsGrouped } from '@/lib/invoice/settings'

export default function InvoiceSettings() {
  const { supabase } = useSupabase()
  const [settings, setSettings] = useState<InvoiceSettings[]>([])
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null)

  useEffect(() => {
    fetchSettings()
  }, [])

  async function fetchSettings() {
    try {
      setLoading(true)
      const { data, error } = await supabase
        .from('invoice_settings')
        .select('*')
        .eq('is_active', true)
        .order('category', { ascending: true })
        .order('setting_key', { ascending: true })

      if (error) throw error
      setSettings(data || [])
    } catch (error) {
      console.error('Error fetching settings:', error)
      setMessage({ type: 'error', text: 'Failed to load settings' })
    } finally {
      setLoading(false)
    }
  }

  async function handleSave(settingKey: string, value: string) {
    try {
      setSaving(true)
      const { error } = await supabase
        .from('invoice_settings')
        .update({ setting_value: value })
        .eq('setting_key', settingKey)

      if (error) throw error

      // Update local state
      setSettings(prev => 
        prev.map(setting => 
          setting.setting_key === settingKey 
            ? { ...setting, setting_value: value }
            : setting
        )
      )

      setMessage({ type: 'success', text: 'Settings saved successfully' })
      setTimeout(() => setMessage(null), 3000)
    } catch (error) {
      console.error('Error saving setting:', error)
      setMessage({ type: 'error', text: 'Failed to save settings' })
    } finally {
      setSaving(false)
    }
  }

  function handleInputChange(settingKey: string, value: string) {
    setSettings(prev => 
      prev.map(setting => 
        setting.setting_key === settingKey 
          ? { ...setting, setting_value: value }
          : setting
      )
    )
  }

  // Group settings by category
  const groupedSettings: InvoiceSettingsGrouped = settings.reduce((acc, setting) => {
    if (!acc[setting.category]) {
      acc[setting.category] = []
    }
    acc[setting.category].push(setting)
    return acc
  }, {} as InvoiceSettingsGrouped)

  if (loading) {
    return (
      <Card>
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading invoice settings...</p>
          </div>
        </div>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {message && (
        <div className={`p-4 rounded-md ${
          message.type === 'success' 
            ? 'bg-green-50 text-green-800 border border-green-200' 
            : 'bg-red-50 text-red-800 border border-red-200'
        }`}>
          {message.text}
        </div>
      )}

      {Object.entries(groupedSettings).map(([category, categorySettings]) => (
        <Card key={category}>
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium text-card-foreground capitalize">
                {category.replace('_', ' ')} Settings
              </h3>
              <p className="text-sm text-muted-foreground mt-1">
                Configure your invoice {category.replace('_', ' ').toLowerCase()} information
              </p>
            </div>

            <div className="grid gap-6">
              {categorySettings.map((setting) => (
                <div key={setting.setting_key} className="space-y-2">
                  <label 
                    htmlFor={setting.setting_key}
                    className="block text-sm font-medium text-card-foreground"
                  >
                    {setting.display_name}
                  </label>
                  {setting.description && (
                    <p className="text-xs text-muted-foreground">
                      {setting.description}
                    </p>
                  )}
                  
                  {setting.input_type === 'textarea' ? (
                    <textarea
                      id={setting.setting_key}
                      value={setting.setting_value || ''}
                      onChange={(e) => handleInputChange(setting.setting_key, e.target.value)}
                      onBlur={(e) => handleSave(setting.setting_key, e.target.value)}
                      className="w-full px-3 py-2 border border-input rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent resize-vertical"
                      rows={3}
                      placeholder={setting.default_value || ''}
                    />
                  ) : (
                    <input
                      id={setting.setting_key}
                      type={setting.input_type || 'text'}
                      value={setting.setting_value || ''}
                      onChange={(e) => handleInputChange(setting.setting_key, e.target.value)}
                      onBlur={(e) => handleSave(setting.setting_key, e.target.value)}
                      className="w-full px-3 py-2 border border-input rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent"
                      placeholder={setting.default_value || ''}
                    />
                  )}
                </div>
              ))}
            </div>
          </div>
        </Card>
      ))}

      {Object.keys(groupedSettings).length === 0 && (
        <Card>
          <div className="text-center py-12">
            <h3 className="text-lg font-medium text-card-foreground mb-2">No Settings Found</h3>
            <p className="text-muted-foreground">
              Invoice settings will appear here once they are configured.
            </p>
          </div>
        </Card>
      )}
    </div>
  )
}
