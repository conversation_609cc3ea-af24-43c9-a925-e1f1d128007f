'use client'

import { useState, useEffect, useCallback } from 'react'
import Link from 'next/link'
import { useSupabase } from '@/lib/supabase/provider'
import { LucidePlus, LucideSearch, LucidePencil, LucideTrash2, LucideMoreHorizontal } from 'lucide-react'
import type { InvoiceData } from '../dashboard/types'

// Membuat type yang lebih spesifik untuk hasil join query
interface InvoiceWithRelations extends InvoiceData {
  clients?: {
    id: string;
    name: string;
    company?: string | null;
  } | null;
  projects?: {
    id: string;
    name: string;
  } | null;
}

export default function InvoicesPage() {
  const { supabase } = useSupabase()
  const [invoices, setInvoices] = useState<InvoiceWithRelations[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  
  // Pagination
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const itemsPerPage = 10
  
  const fetchInvoices = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)
      
      let query = supabase
        .from('invoices')
        .select(`
          id, 
          invoice_number, 
          due_date, 
          issue_date, 
          status, 
          total, 
          subtotal,
          tax_rate,
          tax_amount,
          discount_amount,
          currency, 
          client_id,
          project_id,
          notes,
          clients (
            id, 
            name, 
            company
          ),
          projects (
            id,
            name
          )
        `)
        .order('due_date', { ascending: false })
      
      // Apply search if any
      if (searchTerm) {
        query = query.ilike('invoice_number', `%${searchTerm}%`)
      }
      
      // Apply pagination
      const start = (currentPage - 1) * itemsPerPage
      const end = start + itemsPerPage - 1
      query = query.range(start, end)
      
      const { data, error, count } = await query
      
      if (error) throw error
      
      if (count) {
        setTotalPages(Math.ceil(count / itemsPerPage))
      }
      
      // Menggunakan type InvoiceWithRelations untuk hasil query join
      setInvoices(data as unknown as InvoiceWithRelations[])
    } catch (error) {
      console.error('Error fetching invoices:', error)
      setError(error instanceof Error ? error.message : 'An error occurred while loading invoices')
    } finally {
      setLoading(false)
    }
  }, [supabase, currentPage, searchTerm])
  
  useEffect(() => {
    fetchInvoices()
  }, [fetchInvoices])
  
  // Function to format currency
  const formatCurrency = (value: number | null | undefined, currency: string | null | undefined) => {
    if (value === null || value === undefined) return '-'
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency || 'USD'
    }).format(value)
  }
  
  // Helper function to format dates
  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return 'Not set'
    return new Date(dateString).toLocaleDateString()
  }
  
  // Function to handle search
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    setCurrentPage(1) // Reset to first page on new search
    fetchInvoices()
  }
  
  // Function to handle invoice deletion
  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this invoice?')) return
    
    try {
      const { error } = await supabase
        .from('invoices')
        .delete()
        .eq('id', id)
      
      if (error) throw error
      
      // Refresh the list after deletion
      fetchInvoices()
    } catch (error) {
      console.error('Error deleting invoice:', error)
      alert('Failed to delete invoice. Please try again.')
    }
  }
  
  const getStatusColor = (status: string | null | undefined) => {
    switch (status?.toLowerCase()) {
      case 'paid': return 'bg-green-100 text-green-800'
      case 'sent': return 'bg-blue-100 text-blue-800'
      case 'draft': return 'bg-yellow-100 text-yellow-800'
      case 'overdue': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }
  
  return (
    <div className="container mx-auto px-4 py-6">
      <div className="mb-6 flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900">Invoices</h1>
        <Link
          href="/invoices/new"
          className="inline-flex items-center rounded-md bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700"
        >
          <LucidePlus className="mr-2 h-4 w-4" />
          New Invoice
        </Link>
      </div>
      
      {/* Search */}
      <form onSubmit={handleSearch} className="mb-6">
        <div className="relative mt-1 rounded-md shadow-sm">
          <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
            <LucideSearch className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="block w-full rounded-md border-gray-300 pl-10 focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
            placeholder="Search invoice number..."
          />
          <button
            type="submit"
            className="absolute inset-y-0 right-0 flex items-center rounded-r-md border border-indigo-600 bg-indigo-600 px-4 text-sm font-medium text-white hover:bg-indigo-700"
          >
            Search
          </button>
        </div>
      </form>
      
      {error && (
        <div className="mb-6 rounded-md bg-red-50 p-4">
          <div className="text-sm text-red-700">{error}</div>
        </div>
      )}
      
      {/* Invoices List */}
      <div className="overflow-hidden rounded-lg border border-gray-200 shadow">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                Invoice
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                Client
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                Status
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                Date
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                Amount
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200 bg-white">
            {loading ? (
              <tr>
                <td colSpan={6} className="px-6 py-4 text-center text-sm text-gray-500">
                  Loading invoices...
                </td>
              </tr>
            ) : invoices.length === 0 ? (
              <tr>
                <td colSpan={6} className="px-6 py-4 text-center text-sm text-gray-500">
                  No invoices found.
                </td>
              </tr>
            ) : (
              invoices.map((invoice) => (
                <tr key={invoice.id}>
                  <td className="whitespace-nowrap px-6 py-4">
                    <div className="text-sm font-medium text-gray-900">
                      <Link href={`/invoices/${invoice.id}`} className="hover:text-indigo-600">
                        {invoice.invoice_number}
                      </Link>
                    </div>
                    <div className="text-xs text-gray-500">
                      {invoice.project_id ? (
                        <Link href={`/projects/${invoice.project_id}`} className="hover:text-indigo-600">
                          Project: {invoice.projects?.name}
                        </Link>
                      ) : 'No Project'}
                    </div>
                  </td>
                  <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                    {invoice.clients?.name || 'Unknown'}
                    <br />
                    <span className="text-xs">
                      {invoice.clients?.company || ''}
                    </span>
                  </td>
                  <td className="whitespace-nowrap px-6 py-4">
                    <span className={`inline-flex rounded-full px-2 text-xs font-semibold leading-5 ${getStatusColor(invoice.status)}`}>
                      {invoice.status}
                    </span>
                  </td>
                  <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                    {formatDate(invoice.due_date)}
                    <br />
                    <span className="text-xs">
                      Issued: {formatDate(invoice.issue_date)}
                    </span>
                  </td>
                  <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-900">
                    {formatCurrency(invoice.total, invoice.currency)}
                  </td>
                  <td className="whitespace-nowrap px-6 py-4 text-sm font-medium">
                    <div className="flex space-x-2">
                      <Link 
                        href={`/invoices/${invoice.id}/edit`}
                        className="text-indigo-600 hover:text-indigo-900"
                        title="Edit"
                      >
                        <LucidePencil className="h-5 w-5" />
                      </Link>
                      <button
                        onClick={() => handleDelete(invoice.id)}
                        className="text-red-600 hover:text-red-900"
                        title="Delete"
                      >
                        <LucideTrash2 className="h-5 w-5" />
                      </button>
                      <Link 
                        href={`/invoices/${invoice.id}`}
                        className="text-gray-600 hover:text-gray-900"
                        title="View Details"
                      >
                        <LucideMoreHorizontal className="h-5 w-5" />
                      </Link>
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
      
      {/* Pagination */}
      {totalPages > 1 && (
        <div className="mt-6 flex justify-between">
          <button
            onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
            disabled={currentPage === 1}
            className="inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 disabled:bg-gray-100 disabled:text-gray-400"
          >
            Previous
          </button>
          <p className="text-sm text-gray-700">
            Page <span className="font-medium">{currentPage}</span> of{' '}
            <span className="font-medium">{totalPages}</span>
          </p>
          <button
            onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
            disabled={currentPage === totalPages}
            className="inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 disabled:bg-gray-100 disabled:text-gray-400"
          >
            Next
          </button>
        </div>
      )}
    </div>
  )
}
