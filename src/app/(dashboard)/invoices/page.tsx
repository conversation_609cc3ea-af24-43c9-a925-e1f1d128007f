'use client'

import { useState, useEffect, useCallback } from 'react'
import Link from 'next/link'
import { useSupabase } from '@/lib/supabase/provider'
import { PageLayout, PageHeader, PageContent, TableWrapper, LoadingState, EmptyState } from '@/components/ui/page-layout'
import { Button } from '@/components/ui/button'
import { LucidePlus, LucideSearch, LucidePencil, LucideTrash2, LucideMoreHorizontal } from 'lucide-react'
import type { InvoiceData } from '../dashboard/types'

// Membuat type yang lebih spesifik untuk hasil join query
interface InvoiceWithRelations extends InvoiceData {
  clients?: {
    id: string;
    name: string;
    company?: string | null;
  } | null;
  projects?: {
    id: string;
    name: string;
  } | null;
}

export default function InvoicesPage() {
  const { supabase } = useSupabase()
  const [invoices, setInvoices] = useState<InvoiceWithRelations[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  
  // Pagination
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const itemsPerPage = 10
  
  const fetchInvoices = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)
      
      let query = supabase
        .from('invoices')
        .select(`
          id, 
          invoice_number, 
          due_date, 
          issue_date, 
          status, 
          total, 
          subtotal,
          tax_rate,
          tax_amount,
          discount_amount,
          currency, 
          client_id,
          project_id,
          notes,
          clients (
            id, 
            name, 
            company
          ),
          projects (
            id,
            name
          )
        `)
        .order('due_date', { ascending: false })
      
      // Apply search if any
      if (searchTerm) {
        query = query.ilike('invoice_number', `%${searchTerm}%`)
      }
      
      // Apply pagination
      const start = (currentPage - 1) * itemsPerPage
      const end = start + itemsPerPage - 1
      query = query.range(start, end)
      
      const { data, error, count } = await query
      
      if (error) throw error
      
      if (count) {
        setTotalPages(Math.ceil(count / itemsPerPage))
      }
      
      // Menggunakan type InvoiceWithRelations untuk hasil query join
      setInvoices(data as unknown as InvoiceWithRelations[])
    } catch (error) {
      console.error('Error fetching invoices:', error)
      setError(error instanceof Error ? error.message : 'An error occurred while loading invoices')
    } finally {
      setLoading(false)
    }
  }, [supabase, currentPage, searchTerm])
  
  useEffect(() => {
    fetchInvoices()
  }, [fetchInvoices])
  
  // Function to format currency
  const formatCurrency = (value: number | null | undefined, currency: string | null | undefined) => {
    if (value === null || value === undefined) return '-'
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency || 'USD'
    }).format(value)
  }
  
  // Helper function to format dates
  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return 'Not set'
    return new Date(dateString).toLocaleDateString()
  }
  
  // Function to handle search
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    setCurrentPage(1) // Reset to first page on new search
    fetchInvoices()
  }
  
  // Function to handle invoice deletion
  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this invoice?')) return
    
    try {
      const { error } = await supabase
        .from('invoices')
        .delete()
        .eq('id', id)
      
      if (error) throw error
      
      // Refresh the list after deletion
      fetchInvoices()
    } catch (error) {
      console.error('Error deleting invoice:', error)
      alert('Failed to delete invoice. Please try again.')
    }
  }
  
  const getStatusColor = (status: string | null | undefined) => {
    switch (status?.toLowerCase()) {
      case 'paid': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      case 'sent': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
      case 'draft': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
      case 'overdue': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
      default: return 'bg-muted text-muted-foreground'
    }
  }
  
  if (loading) {
    return (
      <PageLayout>
        <PageContent>
          <LoadingState message="Loading invoices..." />
        </PageContent>
      </PageLayout>
    )
  }

  return (
    <PageLayout>
      <PageHeader
        title="Invoices"
        description="Manage your invoices and billing"
        action={
          <Button asChild>
            <Link href="/invoices/new">
              <LucidePlus className="mr-2 h-4 w-4" />
              New Invoice
            </Link>
          </Button>
        }
      />

      <PageContent>
        {/* Search */}
        <form onSubmit={handleSearch} className="mb-6">
          <div className="relative">
            <LucideSearch className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-20 py-2 border border-input rounded-md bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent"
              placeholder="Search invoice number..."
            />
            <Button
              type="submit"
              size="sm"
              className="absolute right-1 top-1/2 -translate-y-1/2"
            >
              Search
            </Button>
          </div>
        </form>

        {error && (
          <div className="mb-6 p-4 border border-destructive/20 bg-destructive/10 text-destructive rounded-md">
            {error}
          </div>
        )}

        {/* Invoices List */}
        {invoices.length === 0 ? (
          <EmptyState
            title="No invoices found"
            description="Get started by creating your first invoice."
            action={
              <Button asChild>
                <Link href="/invoices/new">
                  <LucidePlus className="mr-2 h-4 w-4" />
                  Create Invoice
                </Link>
              </Button>
            }
          />
        ) : (
          <TableWrapper>
            <table className="w-full">
              <thead>
                <tr className="border-b border-border">
                  <th className="text-left py-3 px-4 font-medium text-muted-foreground">Invoice</th>
                  <th className="text-left py-3 px-4 font-medium text-muted-foreground">Client</th>
                  <th className="text-left py-3 px-4 font-medium text-muted-foreground">Status</th>
                  <th className="text-left py-3 px-4 font-medium text-muted-foreground">Date</th>
                  <th className="text-left py-3 px-4 font-medium text-muted-foreground">Amount</th>
                  <th className="text-left py-3 px-4 font-medium text-muted-foreground">Actions</th>
                </tr>
              </thead>
              <tbody>
                {invoices.map((invoice) => (
                  <tr key={invoice.id} className="border-b border-border hover:bg-muted/50">
                    <td className="py-3 px-4">
                      <div className="font-medium">
                        <Link href={`/invoices/${invoice.id}`} className="text-foreground hover:text-primary">
                          {invoice.invoice_number}
                        </Link>
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {invoice.project_id ? (
                          <Link href={`/projects/${invoice.project_id}`} className="hover:text-primary">
                            Project: {invoice.projects?.name}
                          </Link>
                        ) : 'No Project'}
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <div className="text-foreground">{invoice.clients?.name || 'Unknown'}</div>
                      <div className="text-sm text-muted-foreground">{invoice.clients?.company || ''}</div>
                    </td>
                    <td className="py-3 px-4">
                      <span className={`inline-flex rounded-full px-2 py-1 text-xs font-medium ${getStatusColor(invoice.status)}`}>
                        {invoice.status}
                      </span>
                    </td>
                    <td className="py-3 px-4">
                      <div className="text-foreground">{formatDate(invoice.due_date)}</div>
                      <div className="text-sm text-muted-foreground">Issued: {formatDate(invoice.issue_date)}</div>
                    </td>
                    <td className="py-3 px-4 font-medium">
                      {formatCurrency(invoice.total, invoice.currency)}
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex space-x-2">
                        <Button variant="ghost" size="sm" asChild>
                          <Link href={`/invoices/${invoice.id}/edit`}>
                            <LucidePencil className="h-4 w-4" />
                          </Link>
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDelete(invoice.id)}
                          className="text-destructive hover:text-destructive"
                        >
                          <LucideTrash2 className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm" asChild>
                          <Link href={`/invoices/${invoice.id}`}>
                            <LucideMoreHorizontal className="h-4 w-4" />
                          </Link>
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </TableWrapper>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="mt-6 flex justify-between items-center">
            <Button
              variant="outline"
              onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
            >
              Previous
            </Button>
            <p className="text-sm text-muted-foreground">
              Page <span className="font-medium">{currentPage}</span> of{' '}
              <span className="font-medium">{totalPages}</span>
            </p>
            <Button
              variant="outline"
              onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
              disabled={currentPage === totalPages}
            >
              Next
            </Button>
          </div>
        )}
      </PageContent>
    </PageLayout>
  )
}
