// Helper function to format dates
export function formatDate(dateString: string | null | undefined): string {
  if (!dateString) return '-';
  
  return new Date(dateString).toLocaleDateString('id-ID', {
    day: 'numeric',
    month: 'long',
    year: 'numeric',
  });
}

// Helper function to format currency
export function formatCurrency(value: number | null | undefined, currency: string | null | undefined): string {
  if (value === null || value === undefined) return '-';
  
  // Format untuk IDR dengan locale Indonesia
  if (currency === 'IDR') {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value);
  }
  
  // Default format
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency || 'USD',
    minimumFractionDigits: 2,
  }).format(value);
}

// Function to get status badge color and style
export function getStatusColor(status: string | null | undefined): string {
  if (!status) return 'text-gray-500 bg-gray-100 border border-gray-300';
  
  switch (status.toLowerCase()) {
    case 'paid':
      return 'text-green-800 bg-green-100 border border-green-600';
    case 'pending':
      return 'text-orange-800 bg-orange-100 border border-orange-600';
    case 'overdue':
      return 'text-red-800 bg-red-100 border border-red-600';
    case 'draft':
      return 'text-blue-800 bg-blue-100 border border-blue-600';
    default:
      return 'text-gray-800 bg-gray-100 border border-gray-600';
  }
}
