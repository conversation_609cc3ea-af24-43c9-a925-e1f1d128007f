'use client'

import Link from 'next/link'
import { 
  LucideArrowLeft, 
  LucideFileText, 
  LucidePencil, 
  LucidePrinter, 
  LucideTrash2 
} from 'lucide-react'
import type { InvoiceData } from '../../dashboard/types'

interface InvoiceHeaderProps {
  invoice: InvoiceData
  onDelete: () => void
  deleting: boolean
  getStatusColor: (status: string | undefined | null) => string
}

export default function InvoiceHeader({ invoice, onDelete, deleting, getStatusColor }: InvoiceHeaderProps) {
  return (
    <div className="mb-6 flex items-center justify-between">
      <div className="flex items-center">
        <Link href="/invoices" className="inline-flex items-center text-sm text-indigo-600 hover:text-indigo-900">
          <LucideArrowLeft className="mr-1 h-4 w-4" />
          Back to Invoices
        </Link>
      </div>
      <div>
        <h1 className="text-2xl font-bold text-gray-900">
          Invoice: {invoice.invoice_number}
        </h1>
        <span className={`mt-1 inline-flex rounded-full px-3 py-1 text-sm font-semibold ${getStatusColor(invoice.status)}`}>
          {invoice.status}
        </span>
      </div>
      <div className="flex items-center space-x-3">
        <Link href={`/invoices/${invoice.id}/pdf`} className="p-2 text-gray-500 hover:text-indigo-600 hover:bg-gray-100 rounded-full transition-colors">
          <LucideFileText className="h-5 w-5" />
        </Link>
        <button onClick={() => window.print()} className="p-2 text-gray-500 hover:text-indigo-600 hover:bg-gray-100 rounded-full transition-colors">
          <LucidePrinter className="h-5 w-5" />
        </button>
        <Link href={`/invoices/${invoice.id}/edit`} className="p-2 text-gray-500 hover:text-indigo-600 hover:bg-gray-100 rounded-full transition-colors">
          <LucidePencil className="h-5 w-5" />
        </Link>
        <button disabled={deleting} onClick={onDelete} className="p-2 text-gray-500 hover:text-red-600 hover:bg-gray-100 rounded-full transition-colors">
          {deleting ? (
            <span className="animate-spin">⏳</span>
          ) : (
            <LucideTrash2 className="h-5 w-5" />
          )}
        </button>
      </div>
    </div>
  )
}
