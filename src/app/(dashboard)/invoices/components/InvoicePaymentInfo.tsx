'use client'

import { useState, useEffect } from 'react'
import type { InvoiceData } from '../../dashboard/types'
import { useSupabase } from '@/lib/supabase/provider'
import type { InvoiceSettingsGrouped } from '@/lib/invoice/settings'

interface InvoicePaymentInfoProps {
  invoice: InvoiceData
}

export default function InvoicePaymentInfo({ invoice }: InvoicePaymentInfoProps) {
  const { supabase } = useSupabase()
  const [settings, setSettings] = useState<InvoiceSettingsGrouped | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    async function fetchSettings() {
      try {
        const { data, error } = await supabase
          .from('invoice_settings')
          .select('*')
          .eq('is_active', true)

        if (error) throw error

        // Group settings by category
        const grouped: InvoiceSettingsGrouped = {
          company: {},
          payment: {},
          notes: {},
          template: {}
        }

        data?.forEach(setting => {
          if (setting.setting_value && setting.category in grouped) {
            const category = setting.category as keyof InvoiceSettingsGrouped
            grouped[category][setting.setting_key] = setting.setting_value
          }
        })

        setSettings(grouped)
      } catch (error) {
        console.error('Error fetching invoice settings:', error)
        // Use default values if fetch fails
        setSettings({
          company: {
            company_name: 'PT Harun Studio Indonesia',
            company_email: '<EMAIL>'
          },
          payment: {
            payment_bank_name: 'Bank Central Asia (BCA)',
            payment_account_number: '**********',
            payment_account_name: 'PT Harun Studio Indonesia',
            payment_method: 'Transfer Bank',
            payment_instruction: 'Harap sertakan nomor invoice pada deskripsi pembayaran'
          },
          notes: {
            default_thank_you_message: 'Terima kasih atas kepercayaan Anda menggunakan jasa kami.',
            payment_terms: 'Pembayaran dilakukan dalam waktu 14 hari sejak invoice diterima.',
            contact_message: 'Jika ada pertanyaan mengenai invoice ini, silakan hubungi <NAME_EMAIL>'
          },
          template: {}
        })
      } finally {
        setLoading(false)
      }
    }

    fetchSettings()
  }, [supabase])

  if (loading) {
    return (
      <div className="mt-8 border-t border-gray-200 pt-6">
        <div className="animate-pulse">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-gray-200 h-32 rounded-lg"></div>
            <div className="bg-gray-200 h-32 rounded-lg"></div>
          </div>
        </div>
      </div>
    )
  }

  if (!settings) return null

  return (
    <div className="mt-8 border-t border-gray-200 pt-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Informasi Pembayaran */}
        <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
          <h3 className="text-md font-semibold text-gray-800 mb-3">Informasi Pembayaran</h3>
          <div className="space-y-2 text-sm">
            <p><span className="font-medium">Bank:</span> {settings.payment.payment_bank_name || 'Bank Central Asia (BCA)'}</p>
            <p><span className="font-medium">No. Rekening:</span> {settings.payment.payment_account_number || '**********'}</p>
            <p><span className="font-medium">Atas Nama:</span> {settings.payment.payment_account_name || 'PT Harun Studio Indonesia'}</p>
            <p><span className="font-medium">Metode Pembayaran:</span> {settings.payment.payment_method || 'Transfer Bank'}</p>
            <p className="text-xs text-gray-500 mt-2">{settings.payment.payment_instruction || 'Harap sertakan nomor invoice pada deskripsi pembayaran'}</p>
          </div>
        </div>
        
        {/* Catatan dan Terima Kasih */}
        <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
          <h3 className="text-md font-semibold text-gray-800 mb-3">Catatan</h3>
          <div className="space-y-2 text-sm">
            <p>{invoice.notes || settings.notes.default_thank_you_message || 'Terima kasih atas kepercayaan Anda menggunakan jasa kami.'}</p>
            <p className="mt-3 text-gray-600 italic">{settings.notes.payment_terms || 'Pembayaran dilakukan dalam waktu 14 hari sejak invoice diterima.'}</p>
            <div className="mt-4 pt-2 border-t border-gray-100">
              <p className="text-xs text-gray-500">{settings.notes.contact_message || 'Jika ada pertanyaan mengenai invoice ini, silakan hubungi <NAME_EMAIL>'}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
