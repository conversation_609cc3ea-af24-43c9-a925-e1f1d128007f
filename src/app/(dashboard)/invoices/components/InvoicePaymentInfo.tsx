'use client'

import type { InvoiceData } from '../../dashboard/types'

interface InvoicePaymentInfoProps {
  invoice: InvoiceData
}

export default function InvoicePaymentInfo({ invoice }: InvoicePaymentInfoProps) {
  return (
    <div className="mt-8 border-t border-gray-200 pt-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Informasi Pembayaran */}
        <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
          <h3 className="text-md font-semibold text-gray-800 mb-3">Informasi Pembayaran</h3>
          <div className="space-y-2 text-sm">
            <p><span className="font-medium">Bank:</span> Bank Central Asia (BCA)</p>
            <p><span className="font-medium">No. Rekening:</span> **********</p>
            <p><span className="font-medium">Atas Nama:</span> PT Harun Studio Indonesia</p>
            <p><span className="font-medium">Metode Pembayaran:</span> Transfer Bank</p>
            <p className="text-xs text-gray-500 mt-2">Harap sertakan nomor invoice pada deskripsi pembayaran</p>
          </div>
        </div>
        
        {/* Catatan dan Terima Kasih */}
        <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
          <h3 className="text-md font-semibold text-gray-800 mb-3">Catatan</h3>
          <div className="space-y-2 text-sm">
            <p>{invoice.notes || 'Terima kasih atas kepercayaan Anda menggunakan jasa kami.'}</p>
            <p className="mt-3 text-gray-600 italic">Pembayaran dilakukan dalam waktu 14 hari sejak invoice diterima.</p>
            <div className="mt-4 pt-2 border-t border-gray-100">
              <p className="text-xs text-gray-500">Jika ada pertanyaan mengenai invoice ini, silakan hubungi <NAME_EMAIL></p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
