'use client'

import type { InvoiceData } from '../../dashboard/types'
import { formatDate } from '../../invoices/utils'

export interface ClientData {
  id: string;
  name: string;
  company?: string;
  email?: string;
  phone?: string;
}

export interface ProjectData {
  id: string;
  name: string;
  description?: string;
}

interface InvoiceDetailsProps {
  invoice: InvoiceData
  client: ClientData | null
  project: ProjectData | null
  formatCurrency: (value: number | undefined | null, currency: string | undefined | null) => string
}

export default function InvoiceDetails({ invoice, client, project, formatCurrency }: InvoiceDetailsProps) {
  return (
    <div className="mb-8 grid grid-cols-1 gap-6 md:grid-cols-2">
      <div className="bg-white p-4 rounded-lg border border-gray-200">
        <h3 className="font-semibold text-gray-800 mb-2">Detail Invoice</h3>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <p className="text-sm text-gray-600">No. Invoice</p>
            <p className="font-medium">{invoice.invoice_number || '-'}</p>
          </div>
          <div>
            <p className="text-sm text-gray-600">Tanggal Invoice</p>
            <p className="font-medium">{formatDate(invoice.issue_date)}</p>
          </div>
          <div>
            <p className="text-sm text-gray-600">Jatuh Tempo</p>
            <p className="font-medium">{formatDate(invoice.due_date)}</p>
          </div>
          <div>
            <p className="text-sm text-gray-600">Total</p>
            <p className="font-medium">{formatCurrency(invoice.total || 0, invoice.currency)}</p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        {client && (
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <h3 className="font-semibold text-gray-800 mb-2">Client</h3>
            <p className="font-medium">{client.name}</p>
            {client.company && <p className="text-sm">{client.company}</p>}
            {client.email && <p className="text-sm">{client.email}</p>}
            {client.phone && <p className="text-sm">{client.phone}</p>}
          </div>
        )}

        {project && (
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <h3 className="font-semibold text-gray-800 mb-2">Project</h3>
            <p className="font-medium">{project.name}</p>
            {project.description && <p className="text-sm">{project.description}</p>}
          </div>
        )}
      </div>
    </div>
  )
}
