'use client'

import type { InvoiceData } from '../../dashboard/types'
import { formatDate } from '../../invoices/utils'

export interface ClientData {
  id: string;
  name: string;
  company?: string;
  email?: string;
  phone?: string;
}

export interface ProjectData {
  id: string;
  name: string;
  description?: string;
}

interface InvoiceDetailsProps {
  invoice: InvoiceData
  client: ClientData | null
  project: ProjectData | null
  formatCurrency: (value: number | undefined | null, currency: string | undefined | null) => string
}

export default function InvoiceDetails({ invoice, client, project, formatCurrency }: InvoiceDetailsProps) {
  return (
    <div className="mb-8 grid grid-cols-1 gap-6 md:grid-cols-2">
      <div className="bg-card p-4 rounded-lg border border-border">
        <h3 className="font-semibold text-card-foreground mb-2">Detail Invoice</h3>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <p className="text-sm text-muted-foreground">No. Invoice</p>
            <p className="font-medium text-foreground">{invoice.invoice_number || '-'}</p>
          </div>
          <div>
            <p className="text-sm text-muted-foreground">Tanggal Invoice</p>
            <p className="font-medium text-foreground">{formatDate(invoice.issue_date)}</p>
          </div>
          <div>
            <p className="text-sm text-muted-foreground">Jatuh Tempo</p>
            <p className="font-medium text-foreground">{formatDate(invoice.due_date)}</p>
          </div>
          <div>
            <p className="text-sm text-muted-foreground">Total</p>
            <p className="font-medium text-foreground">{formatCurrency(invoice.total || 0, invoice.currency)}</p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        {client && (
          <div className="bg-card p-4 rounded-lg border border-border">
            <h3 className="font-semibold text-card-foreground mb-2">Client</h3>
            <p className="font-medium text-foreground">{client.name}</p>
            {client.company && <p className="text-sm text-muted-foreground">{client.company}</p>}
            {client.email && <p className="text-sm text-muted-foreground">{client.email}</p>}
            {client.phone && <p className="text-sm text-muted-foreground">{client.phone}</p>}
          </div>
        )}

        {project && (
          <div className="bg-card p-4 rounded-lg border border-border">
            <h3 className="font-semibold text-card-foreground mb-2">Project</h3>
            <p className="font-medium text-foreground">{project.name}</p>
            {project.description && <p className="text-sm text-muted-foreground">{project.description}</p>}
          </div>
        )}
      </div>
    </div>
  )
}
