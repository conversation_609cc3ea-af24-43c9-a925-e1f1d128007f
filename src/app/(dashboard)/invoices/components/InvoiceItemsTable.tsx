'use client'

import { LucidePencil, LucideTrash2, LucidePlus } from 'lucide-react'
import Link from 'next/link'
import type { InvoiceData, InvoiceItem } from '../../dashboard/types'

interface InvoiceItemsTableProps {
  invoice: InvoiceData
  invoiceItems: InvoiceItem[]
  loading: boolean
  onDeleteItem: (id: string) => void
  formatCurrency: (value: number | undefined | null, currency: string | undefined | null) => string
}

export default function InvoiceItemsTable({
  invoice,
  invoiceItems,
  loading,
  onDeleteItem,
  formatCurrency
}: InvoiceItemsTableProps) {
  // Calculate summary rows to avoid conditional rendering issues
  const summaryRows = []

  // Subtotal row
  summaryRows.push(
    <tr key="subtotal" className="border-t-2 border-gray-300">
      <td colSpan={3} className="px-4 py-3 text-right text-sm font-medium text-gray-700">Subtotal</td>
      <td className="px-4 py-3 text-right text-sm font-medium text-gray-900">{formatCurrency(invoice?.subtotal || 0, invoice?.currency)}</td>
      <td className="px-4 py-3"></td>
    </tr>
  )

  // Discount row (if applicable)
  if (invoice?.discount_amount && invoice.discount_amount > 0) {
    summaryRows.push(
      <tr key="discount">
        <td colSpan={3} className="px-4 py-3 text-right text-sm text-gray-700">Discount</td>
        <td className="px-4 py-3 text-right text-sm text-gray-900">-{formatCurrency(invoice.discount_amount, invoice.currency)}</td>
        <td className="px-4 py-3"></td>
      </tr>
    )
  }

  // Tax row (if applicable)
  if (invoice?.tax_amount && invoice.tax_amount > 0) {
    summaryRows.push(
      <tr key="tax">
        <td colSpan={3} className="px-4 py-3 text-right text-sm text-gray-700">Tax {invoice.tax_rate ? `(${invoice.tax_rate}%)` : ''}</td>
        <td className="px-4 py-3 text-right text-sm text-gray-900">{formatCurrency(invoice.tax_amount, invoice.currency)}</td>
        <td className="px-4 py-3"></td>
      </tr>
    )
  }

  // Total row
  summaryRows.push(
    <tr key="total" className="border-t border-b border-gray-300 font-bold bg-gray-100">
      <td colSpan={3} className="px-4 py-4 text-right text-sm font-bold text-gray-900">Total</td>
      <td className="px-4 py-4 text-right text-sm font-bold text-gray-900">{formatCurrency(invoice?.total || 0, invoice?.currency)}</td>
      <td className="px-4 py-4"></td>
    </tr>
  )

  return (
    <div className="mt-8">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-semibold text-gray-900">Invoice Items</h2>
        <Link
          href={`/invoices/${invoice.id}/items/add`}
          className="inline-flex items-center rounded-md border border-indigo-600 bg-indigo-600 px-3 py-2 text-sm font-medium text-white hover:bg-indigo-700"
        >
          <LucidePlus className="mr-1 h-4 w-4" /> Add Item
        </Link>
      </div>

      <div className="overflow-x-auto rounded-lg border border-gray-200">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th scope="col" className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Item</th>
              <th scope="col" className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Qty</th>
              <th scope="col" className="px-4 py-3 text-right text-xs font-medium uppercase tracking-wider text-gray-500">Harga</th>
              <th scope="col" className="px-4 py-3 text-right text-xs font-medium uppercase tracking-wider text-gray-500">Total</th>
              <th scope="col" className="px-4 py-3 text-right text-xs font-medium uppercase tracking-wider text-gray-500">Actions</th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200 bg-white">
            {loading ? (
              <tr>
                <td colSpan={5} className="px-4 py-4 text-center text-sm text-gray-500">Loading items...</td>
              </tr>
            ) : invoiceItems.length > 0 ? (
              invoiceItems.map((item) => (
                <tr key={item.id}>
                  <td className="px-4 py-4 text-sm text-gray-900">
                    <div>
                      <p className="font-medium">{item.description}</p>
                    </div>
                  </td>
                  <td className="px-4 py-4 text-sm text-gray-900">{item.quantity}</td>
                  <td className="px-4 py-4 text-right text-sm text-gray-900">{formatCurrency(item.unit_price, invoice.currency)}</td>
                  <td className="px-4 py-4 text-right text-sm text-gray-900">{formatCurrency(item.amount, invoice.currency)}</td>
                  <td className="px-4 py-4 text-right text-sm font-medium">
                    <div className="flex justify-end space-x-2">
                      <Link href={`/invoices/${invoice.id}/items/${item.id}/edit`} className="p-1 text-gray-500 hover:text-indigo-600 hover:bg-gray-100 rounded-full transition-colors">
                        <LucidePencil className="h-5 w-5" />
                      </Link>
                      <button onClick={() => onDeleteItem(item.id)} className="p-1 text-gray-500 hover:text-red-600 hover:bg-gray-100 rounded-full transition-colors">
                        <LucideTrash2 className="h-5 w-5" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={5} className="px-4 py-4 text-center text-sm text-gray-500">Belum ada item invoice</td>
              </tr>
            )}
          </tbody>
          <tfoot className="bg-gray-50">
            {summaryRows}
          </tfoot>
        </table>
      </div>
    </div>
  )
}
