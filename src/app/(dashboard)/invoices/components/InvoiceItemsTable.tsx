'use client'

import { <PERSON>idePencil, LucideTrash2, LucidePlus } from 'lucide-react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import type { InvoiceData, InvoiceItem } from '../../dashboard/types'

interface InvoiceItemsTableProps {
  invoice: InvoiceData
  invoiceItems: InvoiceItem[]
  loading: boolean
  onDeleteItem: (id: string) => void
  formatCurrency: (value: number | undefined | null, currency: string | undefined | null) => string
}

export default function InvoiceItemsTable({
  invoice,
  invoiceItems,
  loading,
  onDeleteItem,
  formatCurrency
}: InvoiceItemsTableProps) {
  // Calculate summary rows to avoid conditional rendering issues
  const summaryRows = []

  // Subtotal row
  summaryRows.push(
    <tr key="subtotal" className="border-t-2 border-border">
      <td colSpan={3} className="px-4 py-3 text-right text-sm font-medium text-muted-foreground">Subtotal</td>
      <td className="px-4 py-3 text-right text-sm font-medium text-foreground">{formatCurrency(invoice?.subtotal || 0, invoice?.currency)}</td>
      <td className="px-4 py-3"></td>
    </tr>
  )

  // Discount row (if applicable)
  if (invoice?.discount_amount && invoice.discount_amount > 0) {
    summaryRows.push(
      <tr key="discount">
        <td colSpan={3} className="px-4 py-3 text-right text-sm text-muted-foreground">Discount</td>
        <td className="px-4 py-3 text-right text-sm text-foreground">-{formatCurrency(invoice.discount_amount, invoice.currency)}</td>
        <td className="px-4 py-3"></td>
      </tr>
    )
  }

  // Tax row (if applicable)
  if (invoice?.tax_amount && invoice.tax_amount > 0) {
    summaryRows.push(
      <tr key="tax">
        <td colSpan={3} className="px-4 py-3 text-right text-sm text-muted-foreground">Tax {invoice.tax_rate ? `(${invoice.tax_rate}%)` : ''}</td>
        <td className="px-4 py-3 text-right text-sm text-foreground">{formatCurrency(invoice.tax_amount, invoice.currency)}</td>
        <td className="px-4 py-3"></td>
      </tr>
    )
  }

  // Total row
  summaryRows.push(
    <tr key="total" className="border-t border-b border-border font-bold bg-muted/50">
      <td colSpan={3} className="px-4 py-4 text-right text-sm font-bold text-foreground">Total</td>
      <td className="px-4 py-4 text-right text-sm font-bold text-foreground">{formatCurrency(invoice?.total || 0, invoice?.currency)}</td>
      <td className="px-4 py-4"></td>
    </tr>
  )

  return (
    <div className="mt-8">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-semibold text-foreground">Invoice Items</h2>
        <Button asChild>
          <Link href={`/invoices/${invoice.id}/items/add`}>
            <LucidePlus className="mr-1 h-4 w-4" /> Add Item
          </Link>
        </Button>
      </div>

      <div className="overflow-x-auto rounded-lg border border-border">
        <table className="min-w-full divide-y divide-border">
          <thead className="bg-muted/50">
            <tr>
              <th scope="col" className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-muted-foreground">Item</th>
              <th scope="col" className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-muted-foreground">Qty</th>
              <th scope="col" className="px-4 py-3 text-right text-xs font-medium uppercase tracking-wider text-muted-foreground">Harga</th>
              <th scope="col" className="px-4 py-3 text-right text-xs font-medium uppercase tracking-wider text-muted-foreground">Total</th>
              <th scope="col" className="px-4 py-3 text-right text-xs font-medium uppercase tracking-wider text-muted-foreground">Actions</th>
            </tr>
          </thead>
          <tbody className="divide-y divide-border bg-card">
            {loading ? (
              <tr>
                <td colSpan={5} className="px-4 py-4 text-center text-sm text-muted-foreground">Loading items...</td>
              </tr>
            ) : invoiceItems.length > 0 ? (
              invoiceItems.map((item) => (
                <tr key={item.id}>
                  <td className="px-4 py-4 text-sm text-foreground">
                    <div>
                      <p className="font-medium">{item.description}</p>
                    </div>
                  </td>
                  <td className="px-4 py-4 text-sm text-foreground">{item.quantity}</td>
                  <td className="px-4 py-4 text-right text-sm text-foreground">{formatCurrency(item.unit_price, invoice.currency)}</td>
                  <td className="px-4 py-4 text-right text-sm text-foreground">{formatCurrency(item.amount, invoice.currency)}</td>
                  <td className="px-4 py-4 text-right text-sm font-medium">
                    <div className="flex justify-end space-x-2">
                      <Button variant="ghost" size="sm" asChild>
                        <Link href={`/invoices/${invoice.id}/items/${item.id}/edit`}>
                          <LucidePencil className="h-4 w-4" />
                        </Link>
                      </Button>
                      <Button variant="ghost" size="sm" onClick={() => onDeleteItem(item.id)} className="text-destructive hover:text-destructive">
                        <LucideTrash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={5} className="px-4 py-4 text-center text-sm text-muted-foreground">Belum ada item invoice</td>
              </tr>
            )}
          </tbody>
          <tfoot className="bg-muted/50">
            {summaryRows}
          </tfoot>
        </table>
      </div>
    </div>
  )
}
