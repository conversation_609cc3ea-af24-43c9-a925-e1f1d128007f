'use client'

import { useState, useEffect } from 'react'
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation'
import Link from 'next/link'
import { useSupabase } from '@/lib/supabase/provider'
import { LucideArrowLeft, LucideLoader, LucideSave } from 'lucide-react'
import type { InvoiceItem } from '@/app/(dashboard)/dashboard/types'

export default function EditInvoiceItemPage() {
  const params = useParams<{ id: string; itemId: string }>()
  const invoiceId = params.id
  const itemId = params.itemId
  const router = useRouter()
  const { supabase } = useSupabase()
  
  const [description, setDescription] = useState('')
  const [quantity, setQuantity] = useState<number>(1)
  const [unitPrice, setUnitPrice] = useState<number>(0)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)
  
  // Calculate amount based on quantity and unit price
  const amount = quantity * unitPrice
  
  // Fetch invoice item data
  useEffect(() => {
    async function fetchItemData() {
      if (!itemId) return
      
      try {
        setLoading(true)
        setError(null)
        
        const { data, error } = await supabase
          .from('invoice_items')
          .select('*')
          .eq('id', itemId)
          .eq('invoice_id', invoiceId) // Ensure the item belongs to the correct invoice
          .single()
        
        if (error) throw error
        
        if (data) {
          // Populate form fields with existing data
          setDescription(data.description || '')
          setQuantity(data.quantity || 1)
          setUnitPrice(data.unit_price || 0)
        }
      } catch (error) {
        console.error('Error fetching invoice item:', error)
        setError(error instanceof Error ? error.message : 'An error occurred while loading the invoice item')
      } finally {
        setLoading(false)
      }
    }
    
    fetchItemData()
  }, [supabase, itemId, invoiceId])
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!description) {
      setError('Description is required')
      return
    }
    
    try {
      setSaving(true)
      setError(null)
      
      // Update invoice item
      const { error: updateError } = await supabase
        .from('invoice_items')
        .update({
          description,
          quantity,
          unit_price: unitPrice,
          amount
        })
        .eq('id', itemId)
        .eq('invoice_id', invoiceId) // Additional safety check
      
      if (updateError) throw updateError
      
      // Redirect back to invoice detail page
      router.push(`/invoices/${invoiceId}`)
    } catch (error) {
      console.error('Error updating invoice item:', error)
      setError(error instanceof Error ? error.message : 'An error occurred while updating the invoice item')
    } finally {
      setSaving(false)
    }
  }
  
  return (
    <div className="container mx-auto px-4 py-6">
      <div className="mb-6">
        <Link href={`/invoices/${invoiceId}`} className="inline-flex items-center text-sm text-indigo-600 hover:text-indigo-900">
          <LucideArrowLeft className="mr-1 h-4 w-4" />
          Back to Invoice
        </Link>
      </div>
      
      <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
        <h1 className="mb-6 text-2xl font-bold text-gray-900">Edit Invoice Item</h1>
        
        {error && (
          <div className="mb-6 rounded-md bg-red-50 p-4">
            <div className="flex">
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Error</h3>
                <div className="mt-2 text-sm text-red-700">{error}</div>
              </div>
            </div>
          </div>
        )}
        
        {loading ? (
          <div className="py-6 text-center">
            <LucideLoader className="mx-auto h-6 w-6 animate-spin text-indigo-600" />
            <p className="mt-2 text-gray-600">Loading invoice item details...</p>
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                Description
              </label>
              <textarea
                id="description"
                name="description"
                rows={3}
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500"
                placeholder="Enter item description"
                required
              />
            </div>
            
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
              <div>
                <label htmlFor="quantity" className="block text-sm font-medium text-gray-700">
                  Quantity
                </label>
                <input
                  type="number"
                  id="quantity"
                  name="quantity"
                  min="1"
                  step="1"
                  value={quantity}
                  onChange={(e) => setQuantity(Number(e.target.value))}
                  className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500"
                  required
                />
              </div>
              
              <div>
                <label htmlFor="unitPrice" className="block text-sm font-medium text-gray-700">
                  Unit Price
                </label>
                <input
                  type="number"
                  id="unitPrice"
                  name="unitPrice"
                  min="0"
                  step="0.01"
                  value={unitPrice}
                  onChange={(e) => setUnitPrice(Number(e.target.value))}
                  className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500"
                  required
                />
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Total Amount
              </label>
              <div className="mt-1 block w-full rounded-md border border-gray-300 bg-gray-50 px-3 py-2 text-gray-700">
                {new Intl.NumberFormat('en-US', {
                  style: 'currency',
                  currency: 'USD' // Ideally, get this from the invoice currency
                }).format(amount)}
              </div>
            </div>
            
            <div className="flex justify-end pt-4">
              <Link
                href={`/invoices/${invoiceId}`}
                className="mr-4 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50"
              >
                Cancel
              </Link>
              <button
                type="submit"
                disabled={saving}
                className="inline-flex items-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 disabled:bg-indigo-400"
              >
                {saving ? (
                  <>
                    <LucideLoader className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <LucideSave className="mr-2 h-4 w-4" />
                    Update Item
                  </>
                )}
              </button>
            </div>
          </form>
        )}
      </div>
    </div>
  )
}
