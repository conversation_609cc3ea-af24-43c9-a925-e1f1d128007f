'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation' // Menggunakan useParams dari next/navigation
import Link from 'next/link'
import { useSupabase } from '@/lib/supabase/provider'
import { LucideLoader, LucideArrowLeft } from 'lucide-react'
import InvoiceForm from '@/components/forms/InvoiceForm'
import type { InvoiceData } from '@/app/(dashboard)/dashboard/types'
import { PageLayout, PageHeader, PageContent, Card, LoadingState } from '@/components/ui/page-layout'

export default function EditInvoicePage() {
  // Menggunakan useParams() hook sesuai rekomendasi Next.js
  const params = useParams<{ id: string }>()
  const invoiceId = params.id
  const { supabase } = useSupabase()
  const [invoice, setInvoice] = useState<InvoiceData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  useEffect(() => {
    async function fetchInvoice() {
      try {
        setLoading(true)
        const { data, error } = await supabase
          .from('invoices')
          .select('*')
          .eq('id', invoiceId)
          .single()
        
        if (error) throw error
        
        // Debugging what fields are actually coming from database
        console.log('Invoice data from database:', data);
        
        // Log data untuk debugging
        console.log('Invoice data from database:', data);
        
        // Untuk mengatasi masalah TypeScript, kita gunakan type assertion langsung
        // tanpa mencoba mengakses properti yang mungkin tidak ada
        // Ini memungkinkan kita untuk melakukan override tipe sepenuhnya
        // Cast ke unknown dulu, kemudian ke InvoiceData untuk keamanan tipe
        const invoiceWithDefaults = {
          ...data,
          invoice_number: data.invoice_number || '',
          status: data.status || 'draft',
          // Ensure required fields have default values
          total_amount: 0
        };
        
        // Sekarang setInvoice dengan type assertion
        setInvoice(invoiceWithDefaults as unknown as InvoiceData);
      } catch (error) {
        console.error('Error fetching invoice:', error)
        setError('Failed to load invoice details. Please try again.')
      } finally {
        setLoading(false)
      }
    }
    
    fetchInvoice()
  }, [supabase, invoiceId])
  
  if (loading) {
    return (
      <PageLayout>
        <PageContent>
          <LoadingState message="Loading invoice details..." />
        </PageContent>
      </PageLayout>
    )
  }

  if (error) {
    return (
      <PageLayout>
        <PageContent>
          <div className="rounded-md bg-destructive/15 p-4">
            <div className="flex">
              <div className="ml-3">
                <h3 className="text-sm font-medium text-destructive">Error</h3>
                <div className="mt-2 text-sm text-destructive">{error}</div>
              </div>
            </div>
          </div>

          <div className="mt-6">
            <Link
              href={`/invoices/${invoiceId}`}
              className="text-sm text-primary hover:text-primary/80"
            >
              &larr; Back to Invoice Details
            </Link>
          </div>
        </PageContent>
      </PageLayout>
    )
  }

  return (
    <PageLayout>
      <PageHeader
        title="Edit Invoice"
        description={`Make changes to invoice #${invoice?.invoice_number}`}
        breadcrumb={
          <Link
            href={`/invoices/${invoiceId}`}
            className="inline-flex items-center text-sm text-muted-foreground hover:text-foreground"
          >
            <LucideArrowLeft className="mr-1 h-4 w-4" />
            Back to Invoice Details
          </Link>
        }
      />

      <PageContent>
        <Card>
          {invoice && (
            <InvoiceForm
              invoiceId={invoiceId}
              defaultValues={invoice}
            />
          )}
        </Card>
      </PageContent>
    </PageLayout>
  )
}
