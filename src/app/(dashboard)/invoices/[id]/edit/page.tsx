'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation' // Menggunakan useParams dari next/navigation
import Link from 'next/link'
import { useSupabase } from '@/lib/supabase/provider'
import { LucideLoader, LucideArrowLeft } from 'lucide-react'
import InvoiceForm from '@/components/forms/InvoiceForm'
import type { InvoiceData } from '@/app/(dashboard)/dashboard/types'

export default function EditInvoicePage() {
  // Menggunakan useParams() hook sesuai rekomendasi Next.js
  const params = useParams<{ id: string }>()
  const invoiceId = params.id
  const { supabase } = useSupabase()
  const [invoice, setInvoice] = useState<InvoiceData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  useEffect(() => {
    async function fetchInvoice() {
      try {
        setLoading(true)
        const { data, error } = await supabase
          .from('invoices')
          .select('*')
          .eq('id', invoiceId)
          .single()
        
        if (error) throw error
        
        // Debugging what fields are actually coming from database
        console.log('Invoice data from database:', data);
        
        // Log data untuk debugging
        console.log('Invoice data from database:', data);
        
        // Untuk mengatasi masalah TypeScript, kita gunakan type assertion langsung
        // tanpa mencoba mengakses properti yang mungkin tidak ada
        // Ini memungkinkan kita untuk melakukan override tipe sepenuhnya
        // Cast ke unknown dulu, kemudian ke InvoiceData untuk keamanan tipe
        const invoiceWithDefaults = {
          ...data,
          invoice_number: data.invoice_number || '',
          status: data.status || 'draft',
          // Ensure required fields have default values
          total_amount: 0
        };
        
        // Sekarang setInvoice dengan type assertion
        setInvoice(invoiceWithDefaults as unknown as InvoiceData);
      } catch (error) {
        console.error('Error fetching invoice:', error)
        setError('Failed to load invoice details. Please try again.')
      } finally {
        setLoading(false)
      }
    }
    
    fetchInvoice()
  }, [supabase, invoiceId])
  
  if (loading) {
    return (
      <div className="container mx-auto flex h-96 items-center justify-center px-4">
        <div className="text-center">
          <LucideLoader className="mx-auto h-8 w-8 animate-spin text-indigo-600" />
          <p className="mt-2 text-gray-600">Loading invoice details...</p>
        </div>
      </div>
    )
  }
  
  if (error) {
    return (
      <div className="container mx-auto px-4 py-6">
        <div className="rounded-md bg-red-50 p-4">
          <div className="flex">
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error</h3>
              <div className="mt-2 text-sm text-red-700">{error}</div>
            </div>
          </div>
        </div>
        
        <div className="mt-6">
          <Link
            href={`/invoices/${params.id}`}
            className="text-sm text-indigo-600 hover:text-indigo-900"
          >
            &larr; Back to Invoice Details
          </Link>
        </div>
      </div>
    )
  }
  
  return (
    <div className="container mx-auto px-4 py-6">
      <div className="mb-6">
        <Link href={`/invoices/${params.id}`} className="inline-flex items-center text-sm text-indigo-600 hover:text-indigo-900">
          <LucideArrowLeft className="mr-1 h-4 w-4" />
          Back to Invoice Details
        </Link>
      </div>
      
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Edit Invoice</h1>
        <p className="mt-2 text-gray-600">Make changes to invoice #{invoice?.invoice_number}</p>
      </div>
      
      <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
        {invoice && (
          <InvoiceForm
            invoiceId={params.id}
            defaultValues={invoice}
          />
        )}
      </div>
    </div>
  )
}
