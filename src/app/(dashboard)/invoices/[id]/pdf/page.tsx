'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import Link from 'next/link'
import { 
  ArrowLeft as LucideArrowLeft, 
  Download as LucideDownload, 
  Loader as LucideLoader
} from 'lucide-react'
import { useSupabase } from '@/lib/supabase/provider'

export default function InvoicePDFPage() {
  const params = useParams<{ id: string }>()
  const invoiceId = params.id
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [pdfUrl, setPdfUrl] = useState<string | null>(null)
  
  const { supabase } = useSupabase()

  useEffect(() => {
    async function generatePDF() {
      if (!invoiceId) return

      try {
        setLoading(true)
        setError(null)

        // Verifikasi autentikasi terlebih dahulu
        const { data: { session } } = await supabase.auth.getSession()
        if (!session) {
          throw new Error('Authentication required. Please login again.')
        }

        // Call the API endpoint to generate PDF
        const response = await fetch('/api/pdf/invoice', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            // Tambahkan header autentikasi secara eksplisit
            'Authorization': `Bearer ${session.access_token}`
          },
          body: JSON.stringify({ invoiceId }),
          // Penting: mengirim cookies (termasuk auth token) dengan request
          credentials: 'include'
        })

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || 'Failed to generate PDF')
        }

        // Create a blob URL from the PDF response
        const pdfBlob = await response.blob()
        const url = URL.createObjectURL(pdfBlob)
        setPdfUrl(url)
      } catch (err: unknown) {
        console.error('Error generating PDF:', err)
        setError(err instanceof Error ? err.message : 'An error occurred while generating the PDF')
      } finally {
        setLoading(false)
      }
    }

    generatePDF()
  }, [invoiceId, supabase.auth])

  // Separate useEffect for cleanup
  useEffect(() => {
    return () => {
      if (pdfUrl) {
        URL.revokeObjectURL(pdfUrl)
      }
    }
  }, [pdfUrl])
  
  const handleDownload = () => {
    if (pdfUrl) {
      const link = document.createElement('a')
      link.href = pdfUrl
      link.setAttribute('download', `invoice-${invoiceId}.pdf`)
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
  }
  
  return (
    <div className="container mx-auto px-4 py-6">
      <div className="mb-6 flex items-center justify-between">
        <Link href={`/invoices/${invoiceId}`} className="inline-flex items-center text-sm text-indigo-600 hover:text-indigo-900">
          <LucideArrowLeft className="mr-1 h-4 w-4" />
          Back to Invoice
        </Link>
        
        {pdfUrl && (
          <button
            onClick={handleDownload}
            className="inline-flex items-center rounded-md bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700"
          >
            <LucideDownload className="mr-2 h-4 w-4" />
            Download PDF
          </button>
        )}
      </div>
      
      <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
        <h1 className="mb-4 text-xl font-bold text-gray-900">Invoice PDF</h1>
        
        {loading ? (
          <div className="flex h-96 items-center justify-center">
            <div className="text-center">
              <LucideLoader className="mx-auto h-8 w-8 animate-spin text-indigo-600" />
              <p className="mt-2 text-gray-600">Generating PDF...</p>
            </div>
          </div>
        ) : error ? (
          <div className="rounded-md bg-red-50 p-4">
            <div className="flex">
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Error</h3>
                <div className="mt-2 text-sm text-red-700">{error}</div>
              </div>
            </div>
          </div>
        ) : pdfUrl ? (
          <div className="h-screen max-h-[800px] w-full">
            <iframe 
              src={pdfUrl} 
              className="h-full w-full rounded border border-gray-300"
              title="Invoice PDF"
            />
          </div>
        ) : (
          <div className="py-6 text-center text-sm text-gray-500">
            No PDF available
          </div>
        )}
      </div>
    </div>
  )
}
