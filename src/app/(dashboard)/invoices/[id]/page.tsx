'use client'

import { useState, useEffect, useCallback } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { useSupabase } from '@/lib/supabase/provider'
import type { InvoiceData, InvoiceItem } from '../../dashboard/types'

// Import komponen yang sudah direfactor
import InvoiceDetails from '../components/InvoiceDetails'
import InvoiceItemsTable from '../components/InvoiceItemsTable'
import InvoicePaymentInfo from '../components/InvoicePaymentInfo'

// Import utility functions
import { formatCurrency, getStatusColor } from '../utils'

// Import tipe dari komponen untuk memastikan konsistensi
import {
  type ClientData,
  type ProjectData
} from '../components/InvoiceDetails';

// Import layout components
import { PageLayout, PageHeader, PageContent, LoadingState } from '@/components/ui/page-layout'
import { Button } from '@/components/ui/button'
import { LucideArrowLeft, LucideFileText, LucidePencil, LucidePrinter, LucideTrash2 } from 'lucide-react'
import Link from 'next/link'

export default function InvoiceDetailPage() {
  const router = useRouter()
  const params = useParams()
  const { supabase } = useSupabase()
  const invoiceId = params?.id as string
  
  // States
  const [invoice, setInvoice] = useState<InvoiceData | null>(null)
  const [invoiceItems, setInvoiceItems] = useState<InvoiceItem[]>([])
  const [loading, setLoading] = useState(true)
  const [loadingItems, setLoadingItems] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [deleting, setDeleting] = useState(false)

  // Fetch invoice data
  const fetchInvoice = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      const { data, error } = await supabase
        .from('invoices')
        .select(`
          *,
          clients:client_id (*),
          projects:project_id (*)
        `)
        .eq('id', invoiceId)
        .single()

      if (error) {
        throw new Error(error.message)
      }

      setInvoice(data)
    } catch (e: unknown) {
      setError(e instanceof Error ? e.message : 'Error fetching invoice')
    } finally {
      setLoading(false)
    }
  }, [supabase, invoiceId])

  // Fetch invoice items
  const fetchInvoiceItems = useCallback(async () => {
    try {
      setLoadingItems(true)
      const { data, error } = await supabase
        .from('invoice_items')
        .select('*')
        .eq('invoice_id', invoiceId)
        .order('created_at', { ascending: false })

      if (error) {
        throw new Error(error.message)
      }

      setInvoiceItems(data || [])
    } catch (e: Error | unknown) {
      console.error('Error fetching invoice items:', e instanceof Error ? e.message : 'Unknown error')
    } finally {
      setLoadingItems(false)
    }
  }, [supabase, invoiceId])

  // Delete invoice item
  const handleDeleteItem = async (itemId: string) => {
    if (!confirm('Are you sure you want to delete this item?')) {
      return
    }

    try {
      const { error } = await supabase
        .from('invoice_items')
        .delete()
        .eq('id', itemId)

      if (error) {
        throw new Error(error.message)
      }

      // Refresh items and invoice data after delete
      fetchInvoiceItems()
      fetchInvoice()
    } catch (e: unknown) {
      alert(`Failed to delete item: ${e instanceof Error ? e.message : 'Unknown error'}`)
    }
  }

  // Delete invoice
  const handleDelete = async () => {
    if (!confirm('Are you sure you want to delete this invoice?')) {
      return
    }

    try {
      setDeleting(true)

      // Delete all related invoice items first
      const { error: itemsError } = await supabase
        .from('invoice_items')
        .delete()
        .eq('invoice_id', invoiceId)

      if (itemsError) {
        throw new Error(`Failed to delete invoice items: ${itemsError.message}`)
      }

      // Then delete the invoice
      const { error } = await supabase
        .from('invoices')
        .delete()
        .eq('id', invoiceId)

      if (error) {
        throw new Error(error.message)
      }

      router.push('/invoices')
    } catch (e: unknown) {
      alert(`Failed to delete invoice: ${e instanceof Error ? e.message : 'Unknown error'}`)
      setDeleting(false)
    }
  }

  // Load data on component mount
  useEffect(() => {
    fetchInvoice()
    fetchInvoiceItems()
  }, [fetchInvoice, fetchInvoiceItems])

  // Cast related entities ke tipe yang sudah didefinisikan di komponen
  // Dengan type casting yang tepat untuk menangani perbedaan tipe null
  const client = invoice?.clients ? {
    id: invoice.clients.id,
    name: invoice.clients.name,
    company: invoice.clients.company || undefined,
    email: invoice.clients.email || undefined,
    phone: invoice.clients.phone || undefined
  } as ClientData : null;
  
  const project = invoice?.projects ? {
    id: invoice.projects.id,
    name: invoice.projects.name,
    description: invoice.projects.description || undefined
  } as ProjectData : null;
  
  if (loading) {
    return (
      <PageLayout>
        <PageContent>
          <LoadingState message="Loading invoice..." />
        </PageContent>
      </PageLayout>
    )
  }

  if (error) {
    return (
      <PageLayout>
        <PageContent>
          <div className="bg-destructive/15 border border-destructive/20 text-destructive p-4 rounded-md">
            {error}
          </div>
        </PageContent>
      </PageLayout>
    )
  }

  if (!invoice) {
    return (
      <PageLayout>
        <PageContent>
          <div className="text-center py-12">
            <h3 className="text-lg font-medium text-foreground mb-2">Invoice not found</h3>
            <p className="text-muted-foreground">The requested invoice could not be found.</p>
          </div>
        </PageContent>
      </PageLayout>
    )
  }

  return (
    <PageLayout>
      <PageHeader
        title={`Invoice: ${invoice.invoice_number}`}
        description={
          <span className={`inline-flex rounded-full px-3 py-1 text-sm font-medium ${getStatusColor(invoice.status)}`}>
            {invoice.status}
          </span>
        }
        breadcrumb={
          <Link
            href="/invoices"
            className="inline-flex items-center text-sm text-muted-foreground hover:text-foreground"
          >
            <LucideArrowLeft className="mr-1 h-4 w-4" />
            Back to Invoices
          </Link>
        }
        action={
          <div className="flex items-center space-x-2">
            <Button variant="ghost" size="sm" asChild>
              <Link href={`/invoices/${invoice.id}/pdf`}>
                <LucideFileText className="h-4 w-4" />
              </Link>
            </Button>
            <Button variant="ghost" size="sm" onClick={() => window.print()}>
              <LucidePrinter className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" asChild>
              <Link href={`/invoices/${invoice.id}/edit`}>
                <LucidePencil className="h-4 w-4" />
              </Link>
            </Button>
            <Button
              variant="ghost"
              size="sm"
              disabled={deleting}
              onClick={handleDelete}
              className="text-destructive hover:text-destructive"
            >
              {deleting ? (
                <span className="animate-spin">⏳</span>
              ) : (
                <LucideTrash2 className="h-4 w-4" />
              )}
            </Button>
          </div>
        }
      />

      <PageContent className="max-w-5xl">
        <InvoiceDetails
          invoice={invoice}
          client={client}
          project={project}
          formatCurrency={formatCurrency}
        />

        <InvoiceItemsTable
          invoice={invoice}
          invoiceItems={invoiceItems}
          loading={loadingItems}
          onDeleteItem={handleDeleteItem}
          formatCurrency={formatCurrency}
        />

        {!loadingItems && (
          <InvoicePaymentInfo invoice={invoice} />
        )}
      </PageContent>
    </PageLayout>
  )
}
