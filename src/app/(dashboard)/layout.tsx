'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { useSupabase } from '@/lib/supabase/provider'
import { LucideHome, LucideUsers, LucideFileText, LucideBriefcase, LucideMenu, LucideX } from 'lucide-react'

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const router = useRouter()
  const { user, supabase, loading } = useSupabase()
  const [isSidebarOpen, setIsSidebarOpen] = useState(true)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  
  // Check if the user is authenticated
  useEffect(() => {
    if (!loading && !user) {
      router.push('/login')
    }
  }, [user, loading, router])

  const handleSignOut = async () => {
    await supabase.auth.signOut()
    router.push('/login')
  }

  if (loading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <p className="text-lg">Loading...</p>
      </div>
    )
  }

  // Don't render anything if not authenticated (will redirect in useEffect)
  if (!user) return null

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Sidebar for desktop */}
      <aside 
        className={`bg-indigo-700 text-white transition-all duration-300 ease-in-out 
          ${isSidebarOpen ? 'w-64' : 'w-20'} 
          hidden md:block`}
      >
        <div className="flex h-20 items-center justify-between px-4">
          {isSidebarOpen ? (
            <h2 className="text-xl font-bold">HarunStudio</h2>
          ) : (
            <h2 className="text-xl font-bold">HS</h2>
          )}
          <button 
            onClick={() => setIsSidebarOpen(!isSidebarOpen)}
            className="rounded-md p-1 hover:bg-indigo-600"
          >
            {isSidebarOpen ? '<' : '>'}
          </button>
        </div>

        <nav className="mt-6 px-2">
          <SidebarLink 
            href="/dashboard" 
            icon={<LucideHome size={20} />} 
            text="Dashboard" 
            isExpanded={isSidebarOpen} 
          />
          <SidebarLink 
            href="/clients" 
            icon={<LucideUsers size={20} />} 
            text="Clients" 
            isExpanded={isSidebarOpen} 
          />
          <SidebarLink 
            href="/projects" 
            icon={<LucideBriefcase size={20} />} 
            text="Projects" 
            isExpanded={isSidebarOpen} 
          />
          <SidebarLink 
            href="/invoices" 
            icon={<LucideFileText size={20} />} 
            text="Invoices" 
            isExpanded={isSidebarOpen} 
          />
        </nav>

        <div className="mt-auto px-4 pb-6">
          <button
            onClick={handleSignOut}
            className={`mt-6 w-full rounded-md bg-indigo-800 py-2 text-center font-medium text-white hover:bg-indigo-900
              ${isSidebarOpen ? 'px-4' : 'px-2'}`}
          >
            {isSidebarOpen ? 'Sign Out' : 'Exit'}
          </button>
        </div>
      </aside>

      {/* Mobile menu */}
      <div className="absolute inset-y-0 left-0 z-50 md:hidden">
        <button
          className="m-2 rounded-md bg-indigo-700 p-2 text-white"
          onClick={() => setIsMobileMenuOpen(true)}
        >
          <LucideMenu />
        </button>
        
        {/* Mobile sidebar */}
        {isMobileMenuOpen && (
          <div className="fixed inset-0 z-40">
            <div className="absolute inset-0 bg-gray-600 opacity-75" onClick={() => setIsMobileMenuOpen(false)}></div>
            <div className="absolute inset-y-0 left-0 z-10 w-full max-w-xs bg-indigo-700 px-4 py-4 text-white">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-bold">HarunStudio</h2>
                <button
                  onClick={() => setIsMobileMenuOpen(false)}
                  className="rounded-md p-1 hover:bg-indigo-600"
                >
                  <LucideX />
                </button>
              </div>

              <nav className="mt-8">
                <MobileNavLink href="/dashboard" icon={<LucideHome size={20} />} text="Dashboard" onClick={() => setIsMobileMenuOpen(false)} />
                <MobileNavLink href="/clients" icon={<LucideUsers size={20} />} text="Clients" onClick={() => setIsMobileMenuOpen(false)} />
                <MobileNavLink href="/projects" icon={<LucideBriefcase size={20} />} text="Projects" onClick={() => setIsMobileMenuOpen(false)} />
                <MobileNavLink href="/invoices" icon={<LucideFileText size={20} />} text="Invoices" onClick={() => setIsMobileMenuOpen(false)} />
              </nav>

              <div className="mt-auto pt-8">
                <button
                  onClick={handleSignOut}
                  className="w-full rounded-md bg-indigo-800 px-4 py-2 text-center font-medium text-white hover:bg-indigo-900"
                >
                  Sign Out
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Main content */}
      <div className="flex flex-1 flex-col overflow-hidden">
        <header className="bg-white shadow-sm">
          <div className="mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex h-16 items-center justify-between">
              <div className="flex items-center">
                <h1 className="text-2xl font-semibold text-gray-900">HarunStudio Management</h1>
              </div>
              <div className="flex items-center">
                <span className="rounded-full bg-indigo-500 p-2 text-white">
                  {user.email?.charAt(0).toUpperCase()}
                </span>
              </div>
            </div>
          </div>
        </header>
        <main className="flex-1 overflow-auto p-4 sm:p-6 lg:p-8">
          {children}
        </main>
      </div>
    </div>
  )
}

function SidebarLink({ href, icon, text, isExpanded }: { href: string; icon: React.ReactNode; text: string; isExpanded: boolean }) {
  return (
    <Link
      href={href}
      className="mb-2 flex items-center rounded-md p-2 hover:bg-indigo-600"
    >
      <div className="mr-2">{icon}</div>
      {isExpanded && <span>{text}</span>}
    </Link>
  )
}

function MobileNavLink({ href, icon, text, onClick }: { href: string; icon: React.ReactNode; text: string; onClick: () => void }) {
  return (
    <Link
      href={href}
      className="mb-4 flex items-center rounded-md p-2 hover:bg-indigo-600"
      onClick={onClick}
    >
      <div className="mr-3">{icon}</div>
      <span className="text-lg">{text}</span>
    </Link>
  )
}
