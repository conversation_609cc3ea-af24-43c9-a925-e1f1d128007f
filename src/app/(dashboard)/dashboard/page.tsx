'use client'

import { useState, useEffect } from 'react'
import { useSupabase } from '@/lib/supabase/provider'
import type { ProjectData } from './types'
import {
  LucideBriefcase,
  LucideUsers,
  LucideFileText,
  LucideArrowUpRight,
  LucideClock
} from 'lucide-react'

export default function Dashboard() {
  const { supabase } = useSupabase()
  const [counts, setCounts] = useState({
    clients: 0,
    projects: 0,
    invoices: 0,
  })
  const [recentProjects, setRecentProjects] = useState<ProjectData[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    async function fetchDashboardData() {
      try {
        // Fetch counts
        const [clientsResult, projectsResult, invoicesResult, recentProjectsResult] = await Promise.all([
          supabase.from('clients').select('id', { count: 'exact', head: true }),
          supabase.from('projects').select('id', { count: 'exact', head: true }),
          supabase.from('invoices').select('id', { count: 'exact', head: true }),
          supabase.from('projects')
            .select('*, clients(name)')
            .order('created_at', { ascending: false })
            .limit(5)
        ])

        setCounts({
          clients: clientsResult.count ?? 0,
          projects: projectsResult.count ?? 0,
          invoices: invoicesResult.count ?? 0,
        })

        if (recentProjectsResult.data) {
          setRecentProjects(recentProjectsResult.data)
        }
      } catch (error) {
        console.error('Error fetching dashboard data:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchDashboardData()
  }, [supabase])

  return (
    <div className="space-y-6">
      <h1 className="text-3xl font-bold">Dashboard</h1>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
        <StatsCard
          title="Total Clients"
          value={loading ? '...' : counts.clients.toString()}
          icon={<LucideUsers className="h-8 w-8 text-primary" />}
          trend="+12% from last month"
          color="bg-muted"
        />

        <StatsCard
          title="Active Projects"
          value={loading ? '...' : counts.projects.toString()}
          icon={<LucideBriefcase className="h-8 w-8 text-primary" />}
          trend="+5% from last month"
          color="bg-muted"
        />

        <StatsCard
          title="Invoices"
          value={loading ? '...' : counts.invoices.toString()}
          icon={<LucideFileText className="h-8 w-8 text-primary" />}
          trend="+18% from last month"
          color="bg-muted"
        />
      </div>

      {/* Recent Projects */}
      <div className="rounded-lg bg-card border border-border p-6">
        <div className="mb-4 flex items-center justify-between">
          <h2 className="text-xl font-semibold text-card-foreground">Recent Projects</h2>
          <button className="text-sm text-primary hover:text-primary/80">View All</button>
        </div>
        
        <div className="overflow-hidden">
          {loading ? (
            <p>Loading recent projects...</p>
          ) : recentProjects.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead>
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Project</th>
                    <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Client</th>
                    <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Status</th>
                    <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Value</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 bg-white">
                  {recentProjects.map((project) => (
                    <tr key={project.id} className="hover:bg-gray-50">
                      <td className="whitespace-nowrap px-6 py-4">
                        <div className="font-medium text-gray-900">{project.name}</div>
                      </td>
                      <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                        {project.clients?.name || 'N/A'}
                      </td>
                      <td className="whitespace-nowrap px-6 py-4">
                        <StatusBadge status={project.status} />
                      </td>
                      <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                        {project.value ? `${project.currency || 'IDR'} ${project.value.toLocaleString()}` : 'N/A'}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <p className="py-4 text-center text-gray-500">No projects found. Create your first project to get started.</p>
          )}
        </div>
      </div>

      {/* Quick Actions Panel */}
      <div className="rounded-lg bg-card border border-border p-6">
        <h2 className="mb-4 text-xl font-semibold text-card-foreground">Quick Actions</h2>
        <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
          <QuickActionCard 
            title="New Client"
            description="Add a new client to your CRM"
            icon={<LucideUsers className="h-6 w-6" />}
            href="/dashboard/clients/new"
          />
          
          <QuickActionCard 
            title="New Project"
            description="Create a new project record"
            icon={<LucideBriefcase className="h-6 w-6" />}
            href="/dashboard/projects/new"
          />
          
          <QuickActionCard 
            title="Create Invoice"
            description="Generate a new invoice"
            icon={<LucideFileText className="h-6 w-6" />}
            href="/dashboard/invoices/new"
          />
        </div>
      </div>

      {/* Recent Activity (Placeholder) */}
      <div className="rounded-lg bg-card border border-border p-6">
        <h2 className="mb-4 text-xl font-semibold text-card-foreground">Recent Activity</h2>
        <div className="space-y-4">
          <ActivityItem 
            title="New client added"
            description="PT. Maju Bersama was added to your client list"
            time="2 hours ago"
          />
          
          <ActivityItem 
            title="Invoice generated"
            description="Invoice #INV-2025-123 was created for Website Redesign project"
            time="Yesterday"
          />
          
          <ActivityItem 
            title="Project status updated"
            description="E-commerce Development changed from In Progress to Completed"
            time="3 days ago"
          />
        </div>
      </div>
    </div>
  )
}

function StatsCard({ title, value, icon, trend, color }: { title: string; value: string; icon: React.ReactNode; trend: string; color: string }) {
  return (
    <div className={`rounded-lg ${color} p-6 border border-border bg-card`}>
      <div className="flex items-center">
        {icon}
        <h3 className="ml-3 text-lg font-medium text-card-foreground">{title}</h3>
      </div>
      <p className="mt-4 text-3xl font-bold text-card-foreground">{value}</p>
      <div className="mt-1 flex items-center text-sm">
        <LucideArrowUpRight className="mr-1 h-4 w-4 text-muted-foreground" />
        <span className="text-muted-foreground">{trend}</span>
      </div>
    </div>
  )
}

function QuickActionCard({ title, description, icon, href }: { title: string; description: string; icon: React.ReactNode; href: string }) {
  return (
    <a
      href={href}
      className="flex items-start rounded-lg border border-border bg-card p-4 transition-all hover:border-primary/50 hover:bg-accent"
    >
      <div className="mr-4 rounded-full bg-primary/10 p-3 text-primary">
        {icon}
      </div>
      <div>
        <h3 className="font-medium text-card-foreground">{title}</h3>
        <p className="mt-1 text-sm text-muted-foreground">{description}</p>
      </div>
    </a>
  )
}

function ActivityItem({ title, description, time }: { title: string; description: string; time: string }) {
  return (
    <div className="flex space-x-3 border-l-2 border-border pl-4">
      <div className="flex-shrink-0">
        <div className="rounded-full border border-border p-2">
          <LucideClock className="h-4 w-4 text-muted-foreground" />
        </div>
      </div>
      <div>
        <h4 className="font-medium text-card-foreground">{title}</h4>
        <p className="text-sm text-muted-foreground">{description}</p>
        <p className="mt-1 text-xs text-muted-foreground">{time}</p>
      </div>
    </div>
  )
}

function StatusBadge({ status }: { status: string | null }) {
  const getStatusStyles = () => {
    switch (status) {
      case 'in_progress':
        return 'bg-blue-100 text-blue-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'on_hold':
        return 'bg-yellow-100 text-yellow-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };
  
  const getDisplayText = () => {
    switch (status) {
      case 'in_progress':
        return 'In Progress';
      case 'on_hold':
        return 'On Hold';
      case 'cancelled':
        return 'Cancelled';
      default:
        return status ? status.charAt(0).toUpperCase() + status.slice(1) : 'Pending';
    }
  };

  return (
    <span className={`inline-flex rounded-full px-2 py-1 text-xs font-semibold ${getStatusStyles()}`}>
      {getDisplayText()}
    </span>
  );
}
