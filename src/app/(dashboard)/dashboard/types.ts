// Type definitions for the dashboard components
export interface ProjectData {
  id: string;
  name: string;
  client_id: string | null;
  status?: string | null;
  description?: string | null;
  notes?: string | null;
  clients?: { id: string; name: string; company?: string | null } | null;
  start_date?: string | null;
  end_date?: string | null;
  value?: number | null;
  currency?: string | null;
  created_at?: string | null;
  updated_at?: string | null;
}

export interface ClientData {
  id: string;
  name: string;
  company?: string | null;
  email?: string | null;
  phone?: string | null;
  status?: string | null;
}

export interface InvoiceData {
  id: string;
  client_id: string | null;
  project_id?: string | null;
  invoice_number: string;
  due_date: string | null;
  issue_date: string | null; // Sesuai dengan nama kolom di database
  status: string | null;
  total: number; // Sesuai dengan nama kolom di database
  subtotal?: number | null; // Sesuai dengan nama kolom di database
  tax_rate?: number | null;
  tax_amount?: number | null;
  discount_amount?: number | null;
  currency?: string | null;
  notes?: string | null;
  created_at?: string | null;
  updated_at?: string | null;
  // Properti untuk relasi hasil join query
  clients?: {
    id: string;
    name: string;
    company?: string | null;
    email?: string | null;
    phone?: string | null;
  } | null;
  projects?: {
    id: string;
    name: string;
    description?: string | null;
  } | null;
}

export interface InvoiceItem {
  id: string;
  invoice_id: string;
  description: string;
  quantity: number;
  unit_price: number;
  amount: number;
  created_at?: string | null;
  updated_at?: string | null;
}

export interface ProjectMilestone {
  id: string;
  project_id: string;
  name: string;
  description?: string | null;
  due_date?: string | null;
  completed?: boolean;
  completed_at?: string | null;
}
