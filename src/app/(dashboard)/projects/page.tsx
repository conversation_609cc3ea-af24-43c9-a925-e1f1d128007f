'use client'

import { useState, useEffect, useCallback } from 'react'
import Link from 'next/link'
import { useSupabase } from '@/lib/supabase/provider'
import { PageLayout, PageHeader, PageContent, TableWrapper, LoadingState, EmptyState } from '@/components/ui/page-layout'
import { Button } from '@/components/ui/button'
import { LucidePlus, LucideSearch, LucidePencil, LucideTrash2, LucideMoreHorizontal } from 'lucide-react'
import type { ProjectData } from '../dashboard/types'

export default function ProjectsPage() {
  const { supabase } = useSupabase()
  const [projects, setProjects] = useState<ProjectData[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const projectsPerPage = 10

  const fetchProjects = useCallback(async () => {
    try {
      setLoading(true)
      
      let query = supabase
        .from('projects')
        .select('id, name, client_id, status, value, currency, start_date, end_date, clients(name)')
        .order('created_at', { ascending: false })
      
      if (searchTerm) {
        query = query.or(`name.ilike.%${searchTerm}%,status.ilike.%${searchTerm}%`)
      }
      
      const { data, error } = await query
      
      if (error) {
        console.error('Error fetching projects:', error)
        return
      }
      
      setProjects(data as ProjectData[] || [])
    } catch (error) {
      console.error('Error:', error)
    } finally {
      setLoading(false)
    }
  }, [searchTerm, supabase])

  useEffect(() => {
    fetchProjects()
  }, [fetchProjects])

  // Handle search input changes
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value)
    setCurrentPage(1) // Reset to first page when searching
  }

  // Function to format currency
  const formatCurrency = (value: number | null | undefined, currency: string | null | undefined) => {
    if (value === null || value === undefined) return '-'
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency || 'USD'
    }).format(value)
  }

  // Pagination logic
  const indexOfLastProject = currentPage * projectsPerPage
  const indexOfFirstProject = indexOfLastProject - projectsPerPage
  const currentProjects = projects.slice(indexOfFirstProject, indexOfLastProject)
  const totalPages = Math.ceil(projects.length / projectsPerPage)

  const handleDeleteProject = async (projectId: string) => {
    if (window.confirm('Are you sure you want to delete this project? This action cannot be undone.')) {
      try {
        // First check if project has related invoices or milestones
        const { data: relatedData, error: relatedError } = await supabase
          .from('invoices')
          .select('id')
          .eq('project_id', projectId)
          .limit(1)
        
        if (relatedError) {
          console.error('Error checking related data:', relatedError)
          return
        }
        
        // Also check for milestones
        const { data: milestonesData, error: milestonesError } = await supabase
          .from('project_milestones')
          .select('id')
          .eq('project_id', projectId)
          .limit(1)
        
        if (milestonesError) {
          console.error('Error checking related milestones:', milestonesError)
          return
        }
        
        // If project has related data, don't allow deletion
        if (relatedData && relatedData.length > 0) {
          alert('Cannot delete project because it has related invoices.')
          return
        }
        
        if (milestonesData && milestonesData.length > 0) {
          alert('Cannot delete project because it has related milestones. Delete the milestones first.')
          return
        }
        
        // If no related data, proceed with deletion
        const { error } = await supabase
          .from('projects')
          .delete()
          .eq('id', projectId)
        
        if (error) {
          console.error('Error deleting project:', error)
          alert(`Error deleting project: ${error.message}`)
          return
        }
        
        // Refresh projects list
        fetchProjects()
      } catch (error) {
        console.error('Error:', error)
      }
    }
  }

  const getStatusColor = (status: string | null | undefined) => {
    switch (status?.toLowerCase()) {
      case 'active': return 'bg-green-100 text-green-800'
      case 'completed': return 'bg-blue-100 text-blue-800'
      case 'on hold': return 'bg-yellow-100 text-yellow-800'
      case 'cancelled': return 'bg-red-100 text-red-800'
      case 'planning': return 'bg-purple-100 text-purple-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <PageLayout>
      <PageHeader
        title="Projects"
        description="Manage your projects and track their progress"
        action={
          <Button asChild>
            <Link href="/projects/new">
              <LucidePlus className="mr-2 h-4 w-4" />
              New Project
            </Link>
          </Button>
        }
      />

      <PageContent>

        {/* Search and filter */}
        <div className="mb-6 rounded-md border border-border bg-card p-4">
          <div className="flex flex-col space-y-4 md:flex-row md:items-center md:space-x-4 md:space-y-0">
            <div className="relative flex-1">
              <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                <LucideSearch className="h-5 w-5 text-muted-foreground" />
              </div>
              <input
                type="text"
                className="block w-full rounded-md border border-input bg-background py-2 pl-10 text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent"
                placeholder="Search projects..."
                value={searchTerm}
                onChange={handleSearch}
              />
            </div>
          </div>
        </div>

        {/* Projects list */}
        <TableWrapper>
          {loading ? (
            <div className="flex h-64 items-center justify-center">
              <LoadingState message="Loading projects..." />
            </div>
          ) : projects.length === 0 ? (
            <div className="flex h-64 flex-col items-center justify-center">
              <EmptyState
                title="No projects found"
                description="Create your first project to get started"
                action={
                  <Button asChild>
                    <Link href="/projects/new">
                      <LucidePlus className="mr-2 h-4 w-4" />
                      Create your first project
                    </Link>
                  </Button>
                }
              />
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-border">
                <thead className="bg-muted/50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-muted-foreground">Name</th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-muted-foreground">Client</th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-muted-foreground">Status</th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-muted-foreground">Value</th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-muted-foreground">Timeline</th>
                    <th scope="col" className="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider text-muted-foreground">Actions</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-border bg-card">
                  {currentProjects.map((project) => (
                    <tr key={project.id} className="hover:bg-accent">
                      <td className="whitespace-nowrap px-6 py-4">
                        <div className="text-sm font-medium text-card-foreground">{project.name}</div>
                      </td>
                      <td className="whitespace-nowrap px-6 py-4">
                        <div className="text-sm text-muted-foreground">{project.clients?.name || 'No client'}</div>
                      </td>
                      <td className="whitespace-nowrap px-6 py-4">
                        <span className={`inline-flex rounded-full px-2 py-1 text-xs font-semibold ${getStatusColor(project.status)}`}>
                          {project.status || 'Unknown'}
                        </span>
                      </td>
                      <td className="whitespace-nowrap px-6 py-4">
                        <div className="text-sm text-card-foreground">{formatCurrency(project.value, project.currency)}</div>
                      </td>
                      <td className="whitespace-nowrap px-6 py-4">
                        <div className="text-sm text-muted-foreground">
                          {project.start_date ? new Date(project.start_date).toLocaleDateString() : 'Not set'}
                          {project.end_date ? ` - ${new Date(project.end_date).toLocaleDateString()}` : ''}
                        </div>
                      </td>
                      <td className="whitespace-nowrap px-6 py-4 text-right text-sm font-medium">
                        <div className="flex justify-end space-x-2">
                          <Link
                            href={`/projects/${project.id}`}
                            className="rounded px-2 py-1 text-primary hover:bg-accent hover:text-primary/80"
                            title="View"
                          >
                            <LucideMoreHorizontal className="h-4 w-4" />
                        </Link>
                          <Link
                            href={`/projects/${project.id}/edit`}
                            className="rounded px-2 py-1 text-primary hover:bg-accent hover:text-primary/80"
                            title="Edit"
                          >
                            <LucidePencil className="h-4 w-4" />
                          </Link>
                          <button
                            onClick={() => handleDeleteProject(project.id)}
                            className="rounded px-2 py-1 text-destructive hover:bg-accent hover:text-destructive/80"
                            title="Delete"
                          >
                            <LucideTrash2 className="h-4 w-4" />
                          </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}

          {/* Pagination */}
          {!loading && projects.length > 0 && (
            <div className="flex items-center justify-between border-t border-border bg-card px-4 py-3 sm:px-6">
              <div className="hidden sm:block">
                <p className="text-sm text-muted-foreground">
                  Showing <span className="font-medium">{indexOfFirstProject + 1}</span> to{' '}
                  <span className="font-medium">
                    {Math.min(indexOfLastProject, projects.length)}
                  </span>{' '}
                  of <span className="font-medium">{projects.length}</span> projects
                </p>
              </div>
              <div className="flex flex-1 justify-between sm:justify-end">
                <Button
                  variant="outline"
                  onClick={() => setCurrentPage(currentPage - 1)}
                  disabled={currentPage === 1}
                >
                  Previous
                </Button>
                <div className="mx-2 flex">
                  {Array.from({ length: totalPages }, (_, i) => (
                    <Button
                      key={i}
                      variant={currentPage === i + 1 ? "default" : "outline"}
                      size="sm"
                      onClick={() => setCurrentPage(i + 1)}
                      className="mx-1"
                    >
                      {i + 1}
                    </Button>
                  ))}
                </div>
                <Button
                  variant="outline"
                  onClick={() => setCurrentPage(currentPage + 1)}
                  disabled={currentPage === totalPages}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </TableWrapper>
      </PageContent>
    </PageLayout>
  )
}
