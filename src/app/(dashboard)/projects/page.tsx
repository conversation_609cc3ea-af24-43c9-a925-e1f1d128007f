'use client'

import { useState, useEffect, useCallback } from 'react'
import Link from 'next/link'
import { useSupabase } from '@/lib/supabase/provider'
import { LucidePlus, LucideSearch, LucidePencil, LucideTrash2, LucideMoreHorizontal } from 'lucide-react'
import type { ProjectData } from '../dashboard/types'

export default function ProjectsPage() {
  const { supabase } = useSupabase()
  const [projects, setProjects] = useState<ProjectData[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const projectsPerPage = 10

  const fetchProjects = useCallback(async () => {
    try {
      setLoading(true)
      
      let query = supabase
        .from('projects')
        .select('id, name, client_id, status, value, currency, start_date, end_date, clients(name)')
        .order('created_at', { ascending: false })
      
      if (searchTerm) {
        query = query.or(`name.ilike.%${searchTerm}%,status.ilike.%${searchTerm}%`)
      }
      
      const { data, error } = await query
      
      if (error) {
        console.error('Error fetching projects:', error)
        return
      }
      
      setProjects(data as ProjectData[] || [])
    } catch (error) {
      console.error('Error:', error)
    } finally {
      setLoading(false)
    }
  }, [searchTerm, supabase])

  useEffect(() => {
    fetchProjects()
  }, [fetchProjects])

  // Handle search input changes
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value)
    setCurrentPage(1) // Reset to first page when searching
  }

  // Function to format currency
  const formatCurrency = (value: number | null | undefined, currency: string | null | undefined) => {
    if (value === null || value === undefined) return '-'
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency || 'USD'
    }).format(value)
  }

  // Pagination logic
  const indexOfLastProject = currentPage * projectsPerPage
  const indexOfFirstProject = indexOfLastProject - projectsPerPage
  const currentProjects = projects.slice(indexOfFirstProject, indexOfLastProject)
  const totalPages = Math.ceil(projects.length / projectsPerPage)

  const handleDeleteProject = async (projectId: string) => {
    if (window.confirm('Are you sure you want to delete this project? This action cannot be undone.')) {
      try {
        // First check if project has related invoices or milestones
        const { data: relatedData, error: relatedError } = await supabase
          .from('invoices')
          .select('id')
          .eq('project_id', projectId)
          .limit(1)
        
        if (relatedError) {
          console.error('Error checking related data:', relatedError)
          return
        }
        
        // Also check for milestones
        const { data: milestonesData, error: milestonesError } = await supabase
          .from('project_milestones')
          .select('id')
          .eq('project_id', projectId)
          .limit(1)
        
        if (milestonesError) {
          console.error('Error checking related milestones:', milestonesError)
          return
        }
        
        // If project has related data, don't allow deletion
        if (relatedData && relatedData.length > 0) {
          alert('Cannot delete project because it has related invoices.')
          return
        }
        
        if (milestonesData && milestonesData.length > 0) {
          alert('Cannot delete project because it has related milestones. Delete the milestones first.')
          return
        }
        
        // If no related data, proceed with deletion
        const { error } = await supabase
          .from('projects')
          .delete()
          .eq('id', projectId)
        
        if (error) {
          console.error('Error deleting project:', error)
          alert(`Error deleting project: ${error.message}`)
          return
        }
        
        // Refresh projects list
        fetchProjects()
      } catch (error) {
        console.error('Error:', error)
      }
    }
  }

  const getStatusColor = (status: string | null | undefined) => {
    switch (status?.toLowerCase()) {
      case 'active': return 'bg-green-100 text-green-800'
      case 'completed': return 'bg-blue-100 text-blue-800'
      case 'on hold': return 'bg-yellow-100 text-yellow-800'
      case 'cancelled': return 'bg-red-100 text-red-800'
      case 'planning': return 'bg-purple-100 text-purple-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="container mx-auto px-4">
      <div className="mb-8 flex flex-col md:flex-row md:items-center md:justify-between">
        <h1 className="text-2xl font-bold">Projects</h1>
        <div className="mt-4 md:mt-0">
          <Link 
            href="/projects/new" 
            className="inline-flex items-center rounded-md bg-indigo-600 px-4 py-2 text-sm font-medium text-white hover:bg-indigo-700"
          >
            <LucidePlus className="mr-2 h-4 w-4" />
            New Project
          </Link>
        </div>
      </div>

      {/* Search and filter */}
      <div className="mb-6 rounded-md border border-gray-200 bg-white p-4 shadow-sm">
        <div className="flex flex-col space-y-4 md:flex-row md:items-center md:space-x-4 md:space-y-0">
          <div className="relative flex-1">
            <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
              <LucideSearch className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              className="block w-full rounded-md border-0 py-2 pl-10 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600"
              placeholder="Search projects..."
              value={searchTerm}
              onChange={handleSearch}
            />
          </div>
        </div>
      </div>

      {/* Projects list */}
      <div className="rounded-md border border-gray-200 bg-white shadow-sm">
        {loading ? (
          <div className="flex h-64 items-center justify-center">
            <div className="text-center">
              <div className="mb-2 inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-indigo-600 border-r-transparent"></div>
              <p className="text-gray-500">Loading projects...</p>
            </div>
          </div>
        ) : projects.length === 0 ? (
          <div className="flex h-64 flex-col items-center justify-center">
            <p className="mb-4 text-gray-500">No projects found</p>
            <Link 
              href="/projects/new" 
              className="inline-flex items-center rounded-md bg-indigo-600 px-4 py-2 text-sm font-medium text-white hover:bg-indigo-700"
            >
              <LucidePlus className="mr-2 h-4 w-4" />
              Create your first project
            </Link>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Name</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Client</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Status</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Value</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">Timeline</th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider text-gray-500">Actions</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 bg-white">
                {currentProjects.map((project) => (
                  <tr key={project.id} className="hover:bg-gray-50">
                    <td className="whitespace-nowrap px-6 py-4">
                      <div className="text-sm font-medium text-gray-900">{project.name}</div>
                    </td>
                    <td className="whitespace-nowrap px-6 py-4">
                      <div className="text-sm text-gray-500">{project.clients?.name || 'No client'}</div>
                    </td>
                    <td className="whitespace-nowrap px-6 py-4">
                      <span className={`inline-flex rounded-full px-2 py-1 text-xs font-semibold ${getStatusColor(project.status)}`}>
                        {project.status || 'Unknown'}
                      </span>
                    </td>
                    <td className="whitespace-nowrap px-6 py-4">
                      <div className="text-sm text-gray-900">{formatCurrency(project.value, project.currency)}</div>
                    </td>
                    <td className="whitespace-nowrap px-6 py-4">
                      <div className="text-sm text-gray-500">
                        {project.start_date ? new Date(project.start_date).toLocaleDateString() : 'Not set'} 
                        {project.end_date ? ` - ${new Date(project.end_date).toLocaleDateString()}` : ''}
                      </div>
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-right text-sm font-medium">
                      <div className="flex justify-end space-x-2">
                        <Link 
                          href={`/projects/${project.id}`} 
                          className="rounded px-2 py-1 text-indigo-600 hover:bg-indigo-50 hover:text-indigo-900"
                          title="View"
                        >
                          <LucideMoreHorizontal className="h-4 w-4" />
                        </Link>
                        <Link 
                          href={`/projects/${project.id}/edit`} 
                          className="rounded px-2 py-1 text-blue-600 hover:bg-blue-50 hover:text-blue-900"
                          title="Edit"
                        >
                          <LucidePencil className="h-4 w-4" />
                        </Link>
                        <button 
                          onClick={() => handleDeleteProject(project.id)}
                          className="rounded px-2 py-1 text-red-600 hover:bg-red-50 hover:text-red-900"
                          title="Delete"
                        >
                          <LucideTrash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}

        {/* Pagination */}
        {!loading && projects.length > 0 && (
          <div className="flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6">
            <div className="hidden sm:block">
              <p className="text-sm text-gray-700">
                Showing <span className="font-medium">{indexOfFirstProject + 1}</span> to{' '}
                <span className="font-medium">
                  {Math.min(indexOfLastProject, projects.length)}
                </span>{' '}
                of <span className="font-medium">{projects.length}</span> projects
              </p>
            </div>
            <div className="flex flex-1 justify-between sm:justify-end">
              <button
                onClick={() => setCurrentPage(currentPage - 1)}
                disabled={currentPage === 1}
                className="relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:bg-gray-100 disabled:text-gray-400"
              >
                Previous
              </button>
              <div className="mx-2 flex">
                {Array.from({ length: totalPages }, (_, i) => (
                  <button
                    key={i}
                    onClick={() => setCurrentPage(i + 1)}
                    className={`relative inline-flex items-center px-4 py-2 text-sm font-semibold ${
                      currentPage === i + 1
                        ? 'z-10 bg-indigo-600 text-white focus:z-20 focus-visible:outline-offset-2 focus-visible:outline-indigo-600'
                        : 'text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0'
                    }`}
                  >
                    {i + 1}
                  </button>
                ))}
              </div>
              <button
                onClick={() => setCurrentPage(currentPage + 1)}
                disabled={currentPage === totalPages}
                className="relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:bg-gray-100 disabled:text-gray-400"
              >
                Next
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
