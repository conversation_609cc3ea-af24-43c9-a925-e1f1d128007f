'use client'

import { useState, useEffect, useCallback } from 'react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useSupabase } from '@/lib/supabase/provider'
import { LucideEdit, LucideTrash2, LucideChevronLeft, LucidePlus } from 'lucide-react'
import type { ProjectData, InvoiceData } from '../../dashboard/types'
import { useParams } from 'next/navigation'
import { PageLayout, PageHeader, PageContent, PageSection, Card, TableWrapper, LoadingState, EmptyState } from '@/components/ui/page-layout'
import { Button } from '@/components/ui/button'

export default function ProjectDetailPage() {
  const router = useRouter()
  const { supabase } = useSupabase()
  // Menggunakan useParams() hook sesuai rekomendasi Next.js
  const params = useParams<{ id: string }>()
  const projectId = params.id
  
  const [project, setProject] = useState<ProjectData | null>(null)
  const [invoices, setInvoices] = useState<InvoiceData[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchProjectData = useCallback(async () => {
    try {
      setLoading(true)
      
      // Fetch project details
      const { data: projectData, error: projectError } = await supabase
        .from('projects')
        .select('*, clients(id, name, company)')
        .eq('id', projectId)
        .single()
      
      if (projectError) throw projectError
      
      // Fetch related invoices
      try {
        const { data: invoiceData, error: invoiceError } = await supabase
          .from('invoices')
          .select('id, invoice_number, status, total, due_date, issue_date, currency, project_id, client_id')
          .eq('project_id', projectId)
          .order('issue_date', { ascending: false })
        
        if (invoiceError) throw invoiceError
        
        setProject(projectData as ProjectData)
        if (invoiceData) {
          // Ensure data is properly cast to avoid type errors
          setInvoices(invoiceData as unknown as InvoiceData[])
        } else {
          setInvoices([])
        }
      } catch (invoiceErr) {
        console.error('Error fetching invoices:', invoiceErr)
        setInvoices([])
      }
    } catch (error) {
      console.error('Error fetching data:', error)
      setError(error instanceof Error ? error.message : 'An error occurred while loading project data')
    } finally {
      setLoading(false)
    }
  }, [supabase, projectId])

  useEffect(() => {
    fetchProjectData()
  }, [fetchProjectData])

  const handleDeleteProject = async () => {
    if (!window.confirm('Are you sure you want to delete this project? This action cannot be undone.')) {
      return
    }
    
    try {
      // First check if project has related invoices or milestones
      if (invoices.length > 0) {
        alert('Cannot delete project because it has related invoices. Delete the invoices first.')
        return
      }
      
      // Also check for milestones
      const { data: milestonesData, error: milestonesError } = await supabase
        .from('project_milestones')
        .select('id')
        .eq('project_id', projectId)
        .limit(1)
      
      if (milestonesError) {
        throw milestonesError
      }
      
      if (milestonesData && milestonesData.length > 0) {
        alert('Cannot delete project because it has related milestones. Delete the milestones first.')
        return
      }
      
      // If no related data, proceed with deletion
      const { error } = await supabase
        .from('projects')
        .delete()
        .eq('id', projectId)
      
      if (error) throw error
      
      router.push('/projects')
    } catch (error) {
      console.error('Error deleting project:', error)
      alert('An error occurred while deleting the project.')
    }
  }

  // Helper function to format dates
  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return 'Not set'
    return new Date(dateString).toLocaleDateString()
  }
  
  // Helper function to format currency
  const formatCurrency = (value: number | null | undefined, currency: string | null | undefined) => {
    if (value === null || value === undefined) return '-'
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency || 'USD'
    }).format(value)
  }

  // Function to get status badge color
  const getStatusColor = (status: string | null | undefined) => {
    switch (status?.toLowerCase()) {
      case 'active': return 'bg-green-100 text-green-800'
      case 'completed': return 'bg-blue-100 text-blue-800'
      case 'on hold': return 'bg-yellow-100 text-yellow-800'
      case 'cancelled': return 'bg-red-100 text-red-800'
      case 'planning': return 'bg-purple-100 text-purple-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  // Function to get invoice status badge color
  const getInvoiceStatusColor = (status: string | null) => {
    switch (status?.toLowerCase()) {
      case 'paid': return 'bg-green-100 text-green-800'
      case 'sent': return 'bg-blue-100 text-blue-800'
      case 'pending': return 'bg-yellow-100 text-yellow-800'
      case 'overdue': return 'bg-red-100 text-red-800'
      case 'draft': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  if (loading) {
    return (
      <div className="flex h-64 items-center justify-center">
        <div className="text-center">
          <div className="mb-2 inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-indigo-600 border-r-transparent"></div>
          <p className="text-gray-500">Loading project details...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="rounded-md bg-red-50 p-4">
        <div className="text-sm text-red-700">{error}</div>
        <Link href="/projects" className="mt-4 text-sm text-indigo-600 hover:text-indigo-500">
          Back to Projects
        </Link>
      </div>
    )
  }

  if (!project) {
    return (
      <div className="text-center">
        <h2 className="text-xl font-semibold text-gray-900">Project not found</h2>
        <Link href="/projects" className="mt-4 text-sm text-indigo-600 hover:text-indigo-500">
          Back to Projects
        </Link>
      </div>
    )
  }

  return (
    <PageLayout>
      <PageHeader
        title={project.name}
        description="Project details and related information"
        breadcrumb={
          <Link
            href="/projects"
            className="inline-flex items-center text-sm text-muted-foreground hover:text-foreground"
          >
            <LucideChevronLeft className="mr-1 h-4 w-4" />
            Back to Projects
          </Link>
        }
        action={
          <div className="flex space-x-3">
            <Button variant="outline" asChild>
              <Link href={`/projects/${params.id}/edit`}>
                <LucideEdit className="mr-2 h-4 w-4" />
                Edit
              </Link>
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteProject}
            >
              <LucideTrash2 className="mr-2 h-4 w-4" />
              Delete
            </Button>
          </div>
        }
      />

      <PageContent>
        {/* Project details */}
        <div className="mb-8 grid gap-6 md:grid-cols-3">
          {/* Left column - main details */}
          <Card className="col-span-2">
            <h2 className="mb-4 text-xl font-medium text-card-foreground">Project Details</h2>

            <div className="mb-6 space-y-4">
              <div>
                <h3 className="text-sm font-medium text-muted-foreground">Status</h3>
                <span className={`mt-1 inline-flex rounded-full px-2 py-1 text-sm font-semibold ${getStatusColor(project.status)}`}>
                  {project.status || 'Not set'}
                </span>
              </div>

              <div>
                <h3 className="text-sm font-medium text-muted-foreground">Client</h3>
                <p className="mt-1">
                  {project.clients ? (
                    <Link href={`/clients/${project.clients.id}`} className="text-primary hover:text-primary/80">
                      {project.clients.name} {project.clients.company ? `(${project.clients.company})` : ''}
                    </Link>
                  ) : (
                    'No client associated'
                  )}
                </p>
              </div>

              <div>
                <h3 className="text-sm font-medium text-muted-foreground">Timeline</h3>
                <p className="mt-1 text-card-foreground">
                  {formatDate(project.start_date)}
                  {project.start_date && project.end_date && ' to '}
                  {formatDate(project.end_date)}
                </p>
              </div>

              <div>
                <h3 className="text-sm font-medium text-muted-foreground">Value</h3>
                <p className="mt-1 text-lg font-semibold text-card-foreground">
                  {project.value ? formatCurrency(project.value, project.currency) : 'Not set'}
                </p>
              </div>
            </div>

            <div className="mt-6">
              <h3 className="mb-2 text-sm font-medium text-muted-foreground">Description</h3>
              <div className="rounded-md bg-muted/50 p-4 text-sm text-card-foreground">
                {project.description || 'No description provided'}
              </div>
            </div>

            <div className="mt-6">
              <h3 className="mb-2 text-sm font-medium text-muted-foreground">Notes</h3>
              <div className="rounded-md bg-muted/50 p-4 text-sm text-card-foreground">
                {project.notes || 'No notes provided'}
              </div>
            </div>
          </Card>

          {/* Right column - invoices */}
          <div>
            <Card>
              <div className="mb-4 flex items-center justify-between">
                <h2 className="text-lg font-medium text-card-foreground">Invoices</h2>
                <Button size="sm" asChild>
                  <Link href={`/invoices/new?projectId=${projectId}`}>
                    <LucidePlus className="mr-1 h-4 w-4" />
                    New Invoice
                  </Link>
                </Button>
              </div>

              <div className="space-y-3">
                {invoices.length > 0 ? (
                  invoices.map(invoice => (
                    <Link
                      key={invoice.id}
                      href={`/invoices/${invoice.id}`}
                      className="block overflow-hidden rounded-md border border-border bg-card transition hover:bg-accent"
                    >
                      <div className="p-4">
                        <div className="flex items-center justify-between">
                          <h3 className="font-medium text-card-foreground">#{invoice.invoice_number}</h3>
                          <span className={`inline-flex rounded-full px-2 py-1 text-xs font-semibold ${getInvoiceStatusColor(invoice.status)}`}>
                            {invoice.status}
                          </span>
                        </div>
                        <div className="mt-2 flex items-center justify-between text-sm">
                          <span className="text-muted-foreground">
                            {invoice.issue_date ? new Date(invoice.issue_date).toLocaleDateString() : 'No date'}
                          </span>
                          <span className="font-medium text-card-foreground">
                            {formatCurrency(invoice.total, invoice.currency || null)}
                          </span>
                        </div>
                      </div>
                    </Link>
                  ))
                ) : (
                  <EmptyState
                    title="No invoices yet"
                    description="No invoices have been created for this project yet."
                  />
                )}
              </div>
            </Card>

            {/* Project Milestones (placeholder for future implementation) */}
            <Card className="mt-6">
              <div className="mb-4 flex items-center justify-between">
                <h2 className="text-lg font-medium text-card-foreground">Milestones</h2>
                <Button size="sm" asChild>
                  <Link href={`/projects/${projectId}/milestones/new`}>
                    <LucidePlus className="mr-1 h-4 w-4" />
                    Add Milestone
                  </Link>
                </Button>
              </div>

              <EmptyState
                title="No milestones yet"
                description="No milestones have been created for this project yet."
              />
            </Card>
          </div>
        </div>
      </PageContent>
    </PageLayout>
  )
}
