'use client'

import { useState, useEffect, useCallback } from 'react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useSupabase } from '@/lib/supabase/provider'
import { LucideEdit, LucideTrash2, LucideChevronLeft, LucidePlus } from 'lucide-react'
import type { ProjectData, InvoiceData } from '../../dashboard/types'
import { useParams } from 'next/navigation'

export default function ProjectDetailPage() {
  const router = useRouter()
  const { supabase } = useSupabase()
  // Menggunakan useParams() hook sesuai rekomendasi Next.js
  const params = useParams<{ id: string }>()
  const projectId = params.id
  
  const [project, setProject] = useState<ProjectData | null>(null)
  const [invoices, setInvoices] = useState<InvoiceData[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchProjectData = useCallback(async () => {
    try {
      setLoading(true)
      
      // Fetch project details
      const { data: projectData, error: projectError } = await supabase
        .from('projects')
        .select('*, clients(id, name, company)')
        .eq('id', projectId)
        .single()
      
      if (projectError) throw projectError
      
      // Fetch related invoices
      try {
        const { data: invoiceData, error: invoiceError } = await supabase
          .from('invoices')
          .select('id, invoice_number, status, total, due_date, issue_date, currency, project_id, client_id')
          .eq('project_id', projectId)
          .order('issue_date', { ascending: false })
        
        if (invoiceError) throw invoiceError
        
        setProject(projectData as ProjectData)
        if (invoiceData) {
          // Ensure data is properly cast to avoid type errors
          setInvoices(invoiceData as unknown as InvoiceData[])
        } else {
          setInvoices([])
        }
      } catch (invoiceErr) {
        console.error('Error fetching invoices:', invoiceErr)
        setInvoices([])
      }
    } catch (error) {
      console.error('Error fetching data:', error)
      setError(error instanceof Error ? error.message : 'An error occurred while loading project data')
    } finally {
      setLoading(false)
    }
  }, [supabase, projectId])

  useEffect(() => {
    fetchProjectData()
  }, [fetchProjectData])

  const handleDeleteProject = async () => {
    if (!window.confirm('Are you sure you want to delete this project? This action cannot be undone.')) {
      return
    }
    
    try {
      // First check if project has related invoices or milestones
      if (invoices.length > 0) {
        alert('Cannot delete project because it has related invoices. Delete the invoices first.')
        return
      }
      
      // Also check for milestones
      const { data: milestonesData, error: milestonesError } = await supabase
        .from('project_milestones')
        .select('id')
        .eq('project_id', projectId)
        .limit(1)
      
      if (milestonesError) {
        throw milestonesError
      }
      
      if (milestonesData && milestonesData.length > 0) {
        alert('Cannot delete project because it has related milestones. Delete the milestones first.')
        return
      }
      
      // If no related data, proceed with deletion
      const { error } = await supabase
        .from('projects')
        .delete()
        .eq('id', projectId)
      
      if (error) throw error
      
      router.push('/projects')
    } catch (error) {
      console.error('Error deleting project:', error)
      alert('An error occurred while deleting the project.')
    }
  }

  // Helper function to format dates
  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return 'Not set'
    return new Date(dateString).toLocaleDateString()
  }
  
  // Helper function to format currency
  const formatCurrency = (value: number | null | undefined, currency: string | null | undefined) => {
    if (value === null || value === undefined) return '-'
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency || 'USD'
    }).format(value)
  }

  // Function to get status badge color
  const getStatusColor = (status: string | null | undefined) => {
    switch (status?.toLowerCase()) {
      case 'active': return 'bg-green-100 text-green-800'
      case 'completed': return 'bg-blue-100 text-blue-800'
      case 'on hold': return 'bg-yellow-100 text-yellow-800'
      case 'cancelled': return 'bg-red-100 text-red-800'
      case 'planning': return 'bg-purple-100 text-purple-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  // Function to get invoice status badge color
  const getInvoiceStatusColor = (status: string | null) => {
    switch (status?.toLowerCase()) {
      case 'paid': return 'bg-green-100 text-green-800'
      case 'sent': return 'bg-blue-100 text-blue-800'
      case 'pending': return 'bg-yellow-100 text-yellow-800'
      case 'overdue': return 'bg-red-100 text-red-800'
      case 'draft': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  if (loading) {
    return (
      <div className="flex h-64 items-center justify-center">
        <div className="text-center">
          <div className="mb-2 inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-indigo-600 border-r-transparent"></div>
          <p className="text-gray-500">Loading project details...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="rounded-md bg-red-50 p-4">
        <div className="text-sm text-red-700">{error}</div>
        <Link href="/projects" className="mt-4 text-sm text-indigo-600 hover:text-indigo-500">
          Back to Projects
        </Link>
      </div>
    )
  }

  if (!project) {
    return (
      <div className="text-center">
        <h2 className="text-xl font-semibold text-gray-900">Project not found</h2>
        <Link href="/projects" className="mt-4 text-sm text-indigo-600 hover:text-indigo-500">
          Back to Projects
        </Link>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4">
      {/* Breadcrumb and actions */}
      <div className="mb-8 flex flex-wrap items-center justify-between gap-4">
        <div>
          <nav className="flex">
            <Link
              href="/projects"
              className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700"
            >
              <LucideChevronLeft className="mr-1 h-4 w-4" />
              Back to Projects
            </Link>
          </nav>
          <h1 className="mt-2 text-2xl font-bold text-gray-900">{project.name}</h1>
        </div>
        <div className="flex space-x-3">
          <Link
            href={`/projects/${params.id}/edit`}
            className="inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50"
          >
            <LucideEdit className="mr-2 h-4 w-4" />
            Edit
          </Link>
          <button
            onClick={handleDeleteProject}
            className="inline-flex items-center rounded-md border border-transparent bg-red-100 px-4 py-2 text-sm font-medium text-red-700 hover:bg-red-200"
          >
            <LucideTrash2 className="mr-2 h-4 w-4" />
            Delete
          </button>
        </div>
      </div>

      {/* Project details */}
      <div className="mb-8 grid gap-6 md:grid-cols-3">
        {/* Left column - main details */}
        <div className="col-span-2 rounded-lg border bg-white p-6 shadow-sm">
          <h2 className="mb-4 text-xl font-medium">Project Details</h2>
          
          <div className="mb-6 space-y-4">
            <div>
              <h3 className="text-sm font-medium text-gray-500">Status</h3>
              <span className={`mt-1 inline-flex rounded-full px-2 py-1 text-sm font-semibold ${getStatusColor(project.status)}`}>
                {project.status || 'Not set'}
              </span>
            </div>
            
            <div>
              <h3 className="text-sm font-medium text-gray-500">Client</h3>
              <p className="mt-1">
                {project.clients ? (
                  <Link href={`/clients/${project.clients.id}`} className="text-indigo-600 hover:text-indigo-500">
                    {project.clients.name} {project.clients.company ? `(${project.clients.company})` : ''}
                  </Link>
                ) : (
                  'No client associated'
                )}
              </p>
            </div>
            
            <div>
              <h3 className="text-sm font-medium text-gray-500">Timeline</h3>
              <p className="mt-1">
                {formatDate(project.start_date)}
                {project.start_date && project.end_date && ' to '}
                {formatDate(project.end_date)}
              </p>
            </div>
            
            <div>
              <h3 className="text-sm font-medium text-gray-500">Value</h3>
              <p className="mt-1 text-lg font-semibold text-gray-900">
                {project.value ? formatCurrency(project.value, project.currency) : 'Not set'}
              </p>
            </div>
          </div>
          
          <div className="mt-6">
            <h3 className="mb-2 text-sm font-medium text-gray-500">Description</h3>
            <div className="rounded-md bg-gray-50 p-4 text-sm text-gray-900">
              {project.description || 'No description provided'}
            </div>
          </div>
          
          <div className="mt-6">
            <h3 className="mb-2 text-sm font-medium text-gray-500">Notes</h3>
            <div className="rounded-md bg-gray-50 p-4 text-sm text-gray-900">
              {project.notes || 'No notes provided'}
            </div>
          </div>
        </div>

        {/* Right column - invoices */}
        <div>
          <div className="rounded-lg border bg-white p-6 shadow-sm">
            <div className="mb-4 flex items-center justify-between">
              <h2 className="text-lg font-medium">Invoices</h2>
              <Link
                href={`/invoices/new?projectId=${projectId}`}
                className="inline-flex items-center rounded-md bg-indigo-50 px-3 py-1 text-sm font-medium text-indigo-700 hover:bg-indigo-100"
              >
                <LucidePlus className="mr-1 h-4 w-4" />
                New Invoice
              </Link>
            </div>

            <div className="space-y-3">
              {invoices.length > 0 ? (
                invoices.map(invoice => (
                  <Link 
                    key={invoice.id}
                    href={`/invoices/${invoice.id}`}
                    className="block overflow-hidden rounded-md border border-gray-200 bg-white transition hover:bg-gray-50"
                  >
                    <div className="p-4">
                      <div className="flex items-center justify-between">
                        <h3 className="font-medium text-gray-900">#{invoice.invoice_number}</h3>
                        <span className={`inline-flex rounded-full px-2 py-1 text-xs font-semibold ${getInvoiceStatusColor(invoice.status)}`}>
                          {invoice.status}
                        </span>
                      </div>
                      <div className="mt-2 flex items-center justify-between text-sm">
                        <span className="text-gray-500">
                          {invoice.issue_date ? new Date(invoice.issue_date).toLocaleDateString() : 'No date'}
                        </span>
                        <span className="font-medium text-gray-900">
                          {formatCurrency(invoice.total, invoice.currency || null)}
                        </span>
                      </div>
                    </div>
                  </Link>
                ))
              ) : (
                <div className="rounded-md border border-gray-200 p-4 text-center text-sm text-gray-500">
                  No invoices have been created for this project yet.
                </div>
              )}
            </div>
          </div>
          
          {/* Project Milestones (placeholder for future implementation) */}
          <div className="mt-6 rounded-lg border bg-white p-6 shadow-sm">
            <div className="mb-4 flex items-center justify-between">
              <h2 className="text-lg font-medium">Milestones</h2>
              <Link
                href={`/projects/${projectId}/milestones/new`}
                className="inline-flex items-center rounded-md bg-indigo-50 px-3 py-1 text-sm font-medium text-indigo-700 hover:bg-indigo-100"
              >
                <LucidePlus className="mr-1 h-4 w-4" />
                Add Milestone
              </Link>
            </div>
            
            <div className="rounded-md border border-gray-200 p-4 text-center text-sm text-gray-500">
              No milestones have been created for this project yet.
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
