'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation' // Menggunakan useParams dari next/navigation
import Link from 'next/link'
import { useSupabase } from '@/lib/supabase/provider'
import ProjectForm from '@/components/forms/ProjectForm'
import { LucideChevronLeft } from 'lucide-react'
import type { ProjectData } from '../../../dashboard/types'

export default function EditProjectPage() {
  // Menggunakan useParams() hook sesuai rekomendasi Next.js
  const params = useParams<{ id: string }>()
  const projectId = params.id
  const { supabase } = useSupabase()
  const [project, setProject] = useState<ProjectData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function fetchProject() {
      try {
        setLoading(true)
        const { data, error } = await supabase
          .from('projects')
          .select('*')
          .eq('id', projectId)
          .single()
        
        if (error) throw error
        
        setProject(data as ProjectData)
      } catch (error) {
        console.error('Error fetching project:', error)
        setError(error instanceof Error ? error.message : 'An error occurred while loading the project')
      } finally {
        setLoading(false)
      }
    }

    fetchProject()
  }, [supabase, projectId])

  if (loading) {
    return (
      <div className="flex h-64 items-center justify-center">
        <div className="text-center">
          <div className="mb-2 inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-indigo-600 border-r-transparent"></div>
          <p className="text-gray-500">Loading project data...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="rounded-md bg-red-50 p-4">
        <div className="text-sm text-red-700">{error}</div>
        <Link href="/projects" className="mt-4 text-sm text-indigo-600 hover:text-indigo-500">
          Back to Projects
        </Link>
      </div>
    )
  }

  if (!project) {
    return (
      <div className="text-center">
        <h2 className="text-xl font-semibold text-gray-900">Project not found</h2>
        <Link href="/projects" className="mt-4 text-sm text-indigo-600 hover:text-indigo-500">
          Back to Projects
        </Link>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4">
      <div className="mb-8">
        <nav className="flex">
          <Link
            href={`/projects/${projectId}`}
            className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700"
          >
            <LucideChevronLeft className="mr-1 h-4 w-4" />
            Back to Project
          </Link>
        </nav>
        <h1 className="mt-2 text-2xl font-bold">Edit Project</h1>
        <p className="mt-1 text-gray-500">{project.name}</p>
      </div>
      
      <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
        <ProjectForm projectId={projectId} defaultValues={project} />
      </div>
    </div>
  )
}
