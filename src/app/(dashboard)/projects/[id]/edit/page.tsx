'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation' // Menggunakan useParams dari next/navigation
import Link from 'next/link'
import { useSupabase } from '@/lib/supabase/provider'
import ProjectForm from '@/components/forms/ProjectForm'
import { LucideChevronLeft } from 'lucide-react'
import type { ProjectData } from '../../../dashboard/types'
import { PageLayout, PageHeader, PageContent, Card, LoadingState } from '@/components/ui/page-layout'

export default function EditProjectPage() {
  // Menggunakan useParams() hook sesuai rekomendasi Next.js
  const params = useParams<{ id: string }>()
  const projectId = params.id
  const { supabase } = useSupabase()
  const [project, setProject] = useState<ProjectData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function fetchProject() {
      try {
        setLoading(true)
        const { data, error } = await supabase
          .from('projects')
          .select('*')
          .eq('id', projectId)
          .single()
        
        if (error) throw error
        
        setProject(data as ProjectData)
      } catch (error) {
        console.error('Error fetching project:', error)
        setError(error instanceof Error ? error.message : 'An error occurred while loading the project')
      } finally {
        setLoading(false)
      }
    }

    fetchProject()
  }, [supabase, projectId])

  if (loading) {
    return (
      <PageLayout>
        <PageContent>
          <LoadingState message="Loading project data..." />
        </PageContent>
      </PageLayout>
    )
  }

  if (error) {
    return (
      <PageLayout>
        <PageContent>
          <div className="rounded-md bg-destructive/15 p-4">
            <div className="text-sm text-destructive">{error}</div>
            <Link href="/projects" className="mt-4 text-sm text-primary hover:text-primary/80">
              Back to Projects
            </Link>
          </div>
        </PageContent>
      </PageLayout>
    )
  }

  if (!project) {
    return (
      <PageLayout>
        <PageContent>
          <div className="text-center">
            <h2 className="text-xl font-semibold text-card-foreground">Project not found</h2>
            <Link href="/projects" className="mt-4 text-sm text-primary hover:text-primary/80">
              Back to Projects
            </Link>
          </div>
        </PageContent>
      </PageLayout>
    )
  }

  return (
    <PageLayout>
      <PageHeader
        title="Edit Project"
        description={project.name}
        breadcrumb={
          <Link
            href={`/projects/${projectId}`}
            className="inline-flex items-center text-sm text-muted-foreground hover:text-foreground"
          >
            <LucideChevronLeft className="mr-1 h-4 w-4" />
            Back to Project
          </Link>
        }
      />

      <PageContent>
        <Card>
          <ProjectForm projectId={projectId} defaultValues={project} />
        </Card>
      </PageContent>
    </PageLayout>
  )
}
