'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { useSupabase } from '@/lib/supabase/provider'
import { LucideLoader } from 'lucide-react'
import type { ProjectData, ClientData } from '@/app/(dashboard)/dashboard/types'

interface ProjectFormProps {
  projectId?: string
  defaultValues?: ProjectData
}

export default function ProjectForm({ projectId, defaultValues }: ProjectFormProps) {
  const router = useRouter()
  const { supabase } = useSupabase()
  const [loading, setLoading] = useState(false)
  const [submitSuccess, setSubmitSuccess] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [clients, setClients] = useState<ClientData[]>([])
  const [loadingClients, setLoadingClients] = useState(true)
  
  const [formData, setFormData] = useState({
    name: defaultValues?.name || '',
    client_id: defaultValues?.client_id || '',
    // Mengubah default status menjadi 'pending' yang sesuai dengan constraint
    status: defaultValues?.status || 'pending',
    description: defaultValues?.description || '',
    notes: defaultValues?.notes || '',
    start_date: defaultValues?.start_date || '',
    end_date: defaultValues?.end_date || '',
    value: defaultValues?.value || '',
    currency: defaultValues?.currency || 'IDR'
  })

  // Fetch clients for dropdown
  useEffect(() => {
    async function fetchClients() {
      try {
        setLoadingClients(true)
        const { data, error } = await supabase
          .from('clients')
          .select('id, name, company')
          .order('name')
        
        if (error) throw error
        setClients(data || [])
      } catch (error) {
        console.error('Error fetching clients:', error)
      } finally {
        setLoadingClients(false)
      }
    }
    
    fetchClients()
  }, [supabase])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setSubmitSuccess(false)
    setError(null)

    // Convert empty strings to null for optional fields
    // Ensure status is lowercase to match database constraint
    const dataToSubmit = {
      ...formData,
      description: formData.description || null,
      notes: formData.notes || null,
      start_date: formData.start_date || null,
      end_date: formData.end_date || null,
      value: formData.value ? parseFloat(formData.value as string) : null,
      client_id: formData.client_id || null,
      // Ensure status is a valid value that meets the constraint
      status: formData.status.toLowerCase()
    }

    try {
      if (projectId) {
        // Update existing project
        const { error } = await supabase
          .from('projects')
          .update(dataToSubmit)
          .eq('id', projectId)
        
        if (error) {
          console.error('Supabase error updating project:', error)
          setError(`Error updating project: ${error.message || 'Unknown error'}`)
          return
        }
        
        setSubmitSuccess(true)
        setTimeout(() => router.push(`/projects/${projectId}`), 2000)
      } else {
        // Create new project
        const { data, error } = await supabase
          .from('projects')
          .insert(dataToSubmit)
          .select()
        
        if (error) {
          console.error('Supabase error creating project:', error)
          setError(`Error creating project: ${error.message || 'Unknown error'}`)
          return
        }
        
        if (!data || data.length === 0) {
          setError('No data returned from server after creating project')
          return
        }
        
        setSubmitSuccess(true)
        setTimeout(() => router.push(`/projects/${data[0].id}`), 2000)
      }
    } catch (error) {
      console.error('Error saving project:', error)
      setError(
        error instanceof Error 
          ? error.message 
          : typeof error === 'object' && error !== null 
            ? JSON.stringify(error) 
            : 'An unexpected error occurred while saving the project'
      )
    } finally {
      setLoading(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {error && (
        <div className="rounded-md bg-red-50 p-4">
          <div className="text-sm text-red-700">{error}</div>
        </div>
      )}
      
      {submitSuccess && (
        <div className="rounded-md bg-green-50 p-4">
          <div className="text-sm text-green-700">
            Project {projectId ? 'updated' : 'created'} successfully! Redirecting...
          </div>
        </div>
      )}

      <div className="grid gap-6 md:grid-cols-2">
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-700">
            Project Name*
          </label>
          <input
            type="text"
            id="name"
            name="name"
            value={formData.name}
            onChange={handleChange}
            required
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
          />
        </div>

        <div>
          <label htmlFor="client_id" className="block text-sm font-medium text-gray-700">
            Client
          </label>
          <select
            id="client_id"
            name="client_id"
            value={formData.client_id}
            onChange={handleChange}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
          >
            <option value="">Select a client</option>
            {loadingClients ? (
              <option disabled>Loading clients...</option>
            ) : (
              clients.map(client => (
                <option key={client.id} value={client.id}>
                  {client.name} {client.company ? `(${client.company})` : ''}
                </option>
              ))
            )}
          </select>
        </div>

        <div>
          <label htmlFor="status" className="block text-sm font-medium text-gray-700">
            Status*
          </label>
          <select
            id="status"
            name="status"
            value={formData.status}
            onChange={handleChange}
            required
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
          >
            <option value="pending">Pending</option>
            <option value="in_progress">In Progress</option>
            <option value="on_hold">On Hold</option>
            <option value="completed">Completed</option>
            <option value="cancelled">Cancelled</option>
          </select>
        </div>

        <div>
          <label htmlFor="value" className="block text-sm font-medium text-gray-700">
            Project Value
          </label>
          <div className="relative mt-1 rounded-md shadow-sm">
            <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
              <span className="text-gray-500 sm:text-sm">
                {formData.currency === 'USD' ? '$' : 
                 formData.currency === 'EUR' ? '€' : 
                 formData.currency === 'GBP' ? '£' : ''}
              </span>
            </div>
            <input
              type="number"
              id="value"
              name="value"
              value={formData.value}
              onChange={handleChange}
              step="0.01"
              placeholder="0.00"
              className="block w-full rounded-md border-gray-300 pl-7 pr-12 focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
            />
            <div className="absolute inset-y-0 right-0 flex items-center">
              <select
                id="currency"
                name="currency"
                value={formData.currency}
                onChange={handleChange}
                className="h-full rounded-md border-transparent bg-transparent py-0 pl-2 pr-7 text-gray-500 focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              >
                <option value="USD">USD</option>
                <option value="EUR">EUR</option>
                <option value="GBP">GBP</option>
                <option value="IDR">IDR</option>
              </select>
            </div>
          </div>
        </div>

        <div>
          <label htmlFor="start_date" className="block text-sm font-medium text-gray-700">
            Start Date
          </label>
          <input
            type="date"
            id="start_date"
            name="start_date"
            value={formData.start_date}
            onChange={handleChange}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
          />
        </div>

        <div>
          <label htmlFor="end_date" className="block text-sm font-medium text-gray-700">
            End Date
          </label>
          <input
            type="date"
            id="end_date"
            name="end_date"
            value={formData.end_date}
            onChange={handleChange}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
          />
        </div>

      </div>

      <div>
        <label htmlFor="description" className="block text-sm font-medium text-gray-700">
          Description
        </label>
        <textarea
          id="description"
          name="description"
          rows={3}
          value={formData.description}
          onChange={handleChange}
          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
        />
      </div>

      <div>
        <label htmlFor="notes" className="block text-sm font-medium text-gray-700">
          Notes
        </label>
        <textarea
          id="notes"
          name="notes"
          rows={3}
          value={formData.notes}
          onChange={handleChange}
          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
        />
      </div>

      <div className="flex justify-between space-x-4">
        <Link
          href="/projects"
          className="inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
        >
          Cancel
        </Link>

        <button
          type="submit"
          disabled={loading}
          className="inline-flex items-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:bg-indigo-400"
        >
          {loading ? (
            <>
              <LucideLoader className="mr-2 h-4 w-4 animate-spin" />
              Saving...
            </>
          ) : projectId ? 'Update Project' : 'Create Project'}
        </button>
      </div>
    </form>
  )
}
