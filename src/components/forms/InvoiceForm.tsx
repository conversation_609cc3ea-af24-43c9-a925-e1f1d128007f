'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { useSupabase } from '@/lib/supabase/provider'
import { LucideLoader } from 'lucide-react'
import type { InvoiceData, ClientData, ProjectData } from '@/app/(dashboard)/dashboard/types'

interface InvoiceFormProps {
  invoiceId?: string
  defaultValues?: InvoiceData
}

export default function InvoiceForm({ invoiceId, defaultValues }: InvoiceFormProps) {
  const router = useRouter()
  const { supabase } = useSupabase()
  const [loading, setLoading] = useState(false)
  const [submitSuccess, setSubmitSuccess] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [clients, setClients] = useState<ClientData[]>([])
  const [projects, setProjects] = useState<ProjectData[]>([])
  const [loadingClients, setLoadingClients] = useState(true)
  const [loadingProjects, setLoadingProjects] = useState(true)
  
  const [formData, setFormData] = useState({
    invoice_number: defaultValues?.invoice_number || '',
    client_id: defaultValues?.client_id || '',
    project_id: defaultValues?.project_id || '',
    status: defaultValues?.status || 'draft',
    issue_date: defaultValues?.issue_date || new Date().toISOString().split('T')[0],
    due_date: defaultValues?.due_date || '',
    total: defaultValues?.total?.toString() || '',
    currency: defaultValues?.currency || 'USD'
  })

  // Fetch clients for dropdown
  useEffect(() => {
    async function fetchClients() {
      try {
        setLoadingClients(true)
        const { data, error } = await supabase
          .from('clients')
          .select('id, name, company')
          .order('name')
        
        if (error) throw error
        setClients(data || [])
      } catch (error) {
        console.error('Error fetching clients:', error)
      } finally {
        setLoadingClients(false)
      }
    }
    
    fetchClients()
  }, [supabase])

  // Fetch projects based on selected client
  useEffect(() => {
    async function fetchProjects() {
      try {
        setLoadingProjects(true)
        setProjects([]) // Reset projects when client changes
        
        if (!formData.client_id) {
          setLoadingProjects(false)
          return
        }

        const { data, error } = await supabase
          .from('projects')
          .select('id, name')
          .eq('client_id', formData.client_id)
          .order('name')
        
        if (error) throw error
        // Cast dengan aman ke ProjectData[] karena response mungkin hanya memiliki id & name
        setProjects((data || []) as unknown as ProjectData[])
      } catch (error) {
        console.error('Error fetching projects:', error)
      } finally {
        setLoadingProjects(false)
      }
    }
    
    fetchProjects()
  }, [supabase, formData.client_id])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setSubmitSuccess(false)
    setError(null)

    // Convert empty strings to null for optional fields and prepare data
    const dataToSubmit = {
      ...formData,
      project_id: formData.project_id || null,
      issue_date: formData.issue_date || null,
      total: formData.total ? parseFloat(formData.total) : 0,
      // Tambahkan subtotal karena diperlukan oleh database
      subtotal: formData.total ? parseFloat(formData.total) : 0, // Default menggunakan total
      tax_rate: 0, // Default nilai
      tax_amount: 0, // Default nilai
      discount_amount: 0, // Default nilai
      status: formData.status.toLowerCase(),
      currency: formData.currency || 'USD'
    }

    try {
      if (invoiceId) {
        // Update existing invoice
        const { error } = await supabase
          .from('invoices')
          .update(dataToSubmit)
          .eq('id', invoiceId)
        
        if (error) {
          console.error('Supabase error updating invoice:', error)
          setError(`Error updating invoice: ${error.message || 'Unknown error'}`)
          return
        }
        
        setSubmitSuccess(true)
        setTimeout(() => router.push(`/invoices/${invoiceId}`), 2000)
      } else {
        // Create new invoice
        const { data, error } = await supabase
          .from('invoices')
          .insert(dataToSubmit)
          .select()
        
        if (error) {
          console.error('Supabase error creating invoice:', error)
          setError(`Error creating invoice: ${error.message || 'Unknown error'}`)
          return
        }
        
        if (!data || data.length === 0) {
          setError('No data returned from server after creating invoice')
          return
        }
        
        setSubmitSuccess(true)
        setTimeout(() => router.push(`/invoices/${data[0].id}`), 2000)
      }
    } catch (error) {
      console.error('Error saving invoice:', error)
      setError(
        error instanceof Error 
          ? error.message 
          : typeof error === 'object' && error !== null 
            ? JSON.stringify(error) 
            : 'An unexpected error occurred while saving the invoice'
      )
    } finally {
      setLoading(false)
    }
  }

  // Generate a default invoice number
  useEffect(() => {
    if (!defaultValues?.invoice_number && !formData.invoice_number) {
      // Format: INV-YYYYMMDD-XXXX (where XXXX is random)
      const today = new Date()
      const year = today.getFullYear()
      const month = String(today.getMonth() + 1).padStart(2, '0')
      const day = String(today.getDate()).padStart(2, '0')
      const random = Math.floor(1000 + Math.random() * 9000) // 4-digit random number
      
      const invoiceNumber = `INV-${year}${month}${day}-${random}`
      setFormData(prev => ({ ...prev, invoice_number: invoiceNumber }))
    }
  }, [defaultValues, formData.invoice_number])

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {error && (
        <div className="rounded-md bg-red-50 p-4">
          <div className="text-sm text-red-700">{error}</div>
        </div>
      )}
      
      {submitSuccess && (
        <div className="rounded-md bg-green-50 p-4">
          <div className="text-sm text-green-700">
            Invoice {invoiceId ? 'updated' : 'created'} successfully! Redirecting...
          </div>
        </div>
      )}

      <div className="grid gap-6 md:grid-cols-2">
        <div>
          <label htmlFor="invoice_number" className="block text-sm font-medium text-gray-700">
            Invoice Number*
          </label>
          <input
            type="text"
            id="invoice_number"
            name="invoice_number"
            value={formData.invoice_number}
            onChange={handleChange}
            required
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
          />
        </div>

        <div>
          <label htmlFor="status" className="block text-sm font-medium text-gray-700">
            Status*
          </label>
          <select
            id="status"
            name="status"
            value={formData.status}
            onChange={handleChange}
            required
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
          >
            <option value="draft">Draft</option>
            <option value="sent">Sent</option>
            <option value="paid">Paid</option>
            <option value="overdue">Overdue</option>
            <option value="cancelled">Cancelled</option>
          </select>
        </div>

        <div>
          <label htmlFor="client_id" className="block text-sm font-medium text-gray-700">
            Client*
          </label>
          <select
            id="client_id"
            name="client_id"
            value={formData.client_id}
            onChange={handleChange}
            required
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
            disabled={loadingClients}
          >
            <option value="">Select Client</option>
            {clients.map((client) => (
              <option key={client.id} value={client.id}>
                {client.name} {client.company ? `(${client.company})` : ''}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label htmlFor="project_id" className="block text-sm font-medium text-gray-700">
            Project
          </label>
          <select
            id="project_id"
            name="project_id"
            value={formData.project_id}
            onChange={handleChange}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
            disabled={loadingProjects || !formData.client_id}
          >
            <option value="">Select Project (Optional)</option>
            {projects.map((project) => (
              <option key={project.id} value={project.id}>
                {project.name}
              </option>
            ))}
          </select>
          {formData.client_id && projects.length === 0 && !loadingProjects && (
            <p className="mt-1 text-xs text-gray-500">No projects found for this client.</p>
          )}
        </div>

        <div>
          <label htmlFor="issue_date" className="block text-sm font-medium text-gray-700">
            Issue Date
          </label>
          <input
            type="date"
            id="issue_date"
            name="issue_date"
            value={formData.issue_date}
            onChange={handleChange}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
          />
        </div>

        <div>
          <label htmlFor="due_date" className="block text-sm font-medium text-gray-700">
            Due Date*
          </label>
          <input
            type="date"
            id="due_date"
            name="due_date"
            value={formData.due_date}
            onChange={handleChange}
            required
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
          />
        </div>

        <div>
          <label htmlFor="total" className="block text-sm font-medium text-gray-700">
            Total Amount*
          </label>
          <div className="relative mt-1 rounded-md shadow-sm">
            <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
              <span className="text-gray-500 sm:text-sm">
                {formData.currency === 'USD' ? '$' : 
                 formData.currency === 'EUR' ? '€' : 
                 formData.currency === 'GBP' ? '£' : ''}
              </span>
            </div>
            <input
              type="number"
              id="total"
              name="total"
              value={formData.total}
              onChange={handleChange}
              step="0.01"
              required
              placeholder="0.00"
              className="block w-full rounded-md border-gray-300 pl-7 pr-12 focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
            />
            <div className="absolute inset-y-0 right-0 flex items-center">
              <select
                id="currency"
                name="currency"
                value={formData.currency}
                onChange={handleChange}
                className="h-full rounded-md border-transparent bg-transparent py-0 pl-2 pr-7 text-gray-500 focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              >
                <option value="USD">USD</option>
                <option value="EUR">EUR</option>
                <option value="GBP">GBP</option>
                <option value="IDR">IDR</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      <div className="flex justify-between space-x-4">
        <Link
          href="/invoices"
          className="inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
        >
          Cancel
        </Link>

        <button
          type="submit"
          disabled={loading}
          className="inline-flex items-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:bg-indigo-400"
        >
          {loading ? (
            <>
              <LucideLoader className="mr-2 h-4 w-4 animate-spin" />
              Saving...
            </>
          ) : invoiceId ? 'Update Invoice' : 'Create Invoice'}
        </button>
      </div>
    </form>
  )
}
