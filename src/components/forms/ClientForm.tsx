'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { useSupabase } from '@/lib/supabase/provider'
import type { ClientData } from '@/app/(dashboard)/dashboard/types'

interface ClientFormProps {
  clientId?: string
  defaultValues?: Partial<ClientData>
}

export default function ClientForm({ clientId, defaultValues = {} }: ClientFormProps) {
  const router = useRouter()
  const { supabase } = useSupabase()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  
  // Form state
  const [formState, setFormState] = useState<Partial<ClientData>>({
    name: '',
    company: '',
    email: '',
    phone: '',
    status: 'active',
    ...defaultValues
  })
  
  useEffect(() => {
    if (clientId) {
      fetchClient()
    }
  }, [clientId])
  
  const fetchClient = async () => {
    if (!clientId) return
    
    try {
      setLoading(true)
      const { data, error } = await supabase
        .from('clients')
        .select('*')
        .eq('id', clientId)
        .single()
      
      if (error) {
        throw error
      }
      
      if (data) {
        setFormState(data)
      }
    } catch (error: any) {
      setError(error.message || 'Failed to load client data')
    } finally {
      setLoading(false)
    }
  }
  
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormState(prev => ({
      ...prev,
      [name]: value
    }))
  }
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError(null)
    setSuccess(null)
    
    try {
      // Validate required fields
      if (!formState.name) {
        setError('Name is required')
        setLoading(false)
        return
      }
      
      if (clientId) {
        // Update existing client
        const { error } = await supabase
          .from('clients')
          .update({
            name: formState.name,
            company: formState.company,
            email: formState.email,
            phone: formState.phone,
            status: formState.status,
          })
          .eq('id', clientId)
        
        if (error) throw error
        setSuccess('Client updated successfully')
        
        // Wait a moment before redirecting
        setTimeout(() => {
          router.push('/clients')
          router.refresh()
        }, 1500)
      } else {
        // Create new client
        const { data, error } = await supabase
          .from('clients')
          .insert({
            name: formState.name,
            company: formState.company,
            email: formState.email,
            phone: formState.phone,
            status: formState.status,
          })
          .select()
        
        if (error) throw error
        setSuccess('Client created successfully')
        
        // Wait a moment before redirecting
        setTimeout(() => {
          router.push('/clients')
          router.refresh()
        }, 1500)
      }
    } catch (error: any) {
      setError(error.message || 'An error occurred while saving the client')
      console.error('Error saving client:', error)
    } finally {
      setLoading(false)
    }
  }
  
  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {error && (
        <div className="rounded-md bg-red-50 p-4">
          <div className="text-sm text-red-700">{error}</div>
        </div>
      )}
      
      {success && (
        <div className="rounded-md bg-green-50 p-4">
          <div className="text-sm text-green-700">{success}</div>
        </div>
      )}
      
      <div className="space-y-4">
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-700">
            Name <span className="text-red-500">*</span>
          </label>
          <input
            id="name"
            name="name"
            type="text"
            value={formState.name || ''}
            onChange={handleChange}
            required
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
            placeholder="Client name"
          />
        </div>
        
        <div>
          <label htmlFor="company" className="block text-sm font-medium text-gray-700">
            Company
          </label>
          <input
            id="company"
            name="company"
            type="text"
            value={formState.company || ''}
            onChange={handleChange}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
            placeholder="Company name"
          />
        </div>
        
        <div>
          <label htmlFor="email" className="block text-sm font-medium text-gray-700">
            Email
          </label>
          <input
            id="email"
            name="email"
            type="email"
            value={formState.email || ''}
            onChange={handleChange}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
            placeholder="<EMAIL>"
          />
        </div>
        
        <div>
          <label htmlFor="phone" className="block text-sm font-medium text-gray-700">
            Phone
          </label>
          <input
            id="phone"
            name="phone"
            type="tel"
            value={formState.phone || ''}
            onChange={handleChange}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
            placeholder="Phone number"
          />
        </div>
        
        <div>
          <label htmlFor="status" className="block text-sm font-medium text-gray-700">
            Status
          </label>
          <select
            id="status"
            name="status"
            value={formState.status || 'active'}
            onChange={handleChange}
            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
          >
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
            <option value="lead">Lead</option>
          </select>
        </div>
      </div>
      
      <div className="flex items-center justify-end space-x-4 pt-4">
        <Link
          href="/clients"
          className="rounded-md border border-gray-300 bg-white py-2 px-4 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
        >
          Cancel
        </Link>
        <button
          type="submit"
          disabled={loading}
          className="rounded-md border border-transparent bg-indigo-600 py-2 px-4 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:bg-indigo-400"
        >
          {loading ? 'Saving...' : clientId ? 'Update Client' : 'Create Client'}
        </button>
      </div>
    </form>
  )
}
